import React, { useState, useEffect } from 'react';
import { getServerMembers } from '../services/serverService';
import { ServerMember } from '../types';
import StatusIndicator from './ui/StatusIndicator';
import Tooltip from './ui/Tooltip';

interface MemberListProps {
  serverId: string;
  isVisible?: boolean;
}

const MemberList: React.FC<MemberListProps> = ({ serverId, isVisible = true }) => {
  const [members, setMembers] = useState<ServerMember[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchMembers = async () => {
      if (!serverId) return;
      
      try {
        setLoading(true);
        const serverMembers = await getServerMembers(serverId);
        setMembers(serverMembers);
      } catch (err) {
        console.error('Error fetching members:', err);
        setError('Failed to load members');
      } finally {
        setLoading(false);
      }
    };

    fetchMembers();
  }, [serverId]);

  // Group members by status
  const groupedMembers = members.reduce((groups: Record<string, ServerMember[]>, member) => {
    const status = member.profiles.status || 'offline';
    if (!groups[status]) {
      groups[status] = [];
    }
    groups[status].push(member);
    return groups;
  }, {});

  // Sort groups by priority
  const statusOrder = ['online', 'idle', 'dnd', 'offline'];
  const sortedGroups = statusOrder.map(status => ({
    status,
    members: groupedMembers[status] || []
  })).filter(group => group.members.length > 0);

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'online':
        return 'Online';
      case 'idle':
        return 'Idle';
      case 'dnd':
        return 'Do Not Disturb';
      case 'offline':
        return 'Offline';
      default:
        return 'Unknown';
    }
  };

  const getTotalOnlineCount = () => {
    return members.filter(member => 
      member.profiles.status && member.profiles.status !== 'offline'
    ).length;
  };

  if (!isVisible) return null;

  return (
    <div className="w-60 bg-discord-darker flex flex-col border-l border-discord-separator">
      {/* Header */}
      <div className="p-4 border-b border-discord-separator">
        <h3 className="text-xs font-semibold text-discord-channel uppercase tracking-wider">
          Members — {members.length}
        </h3>
      </div>

      {/* Members list */}
      <div className="flex-1 overflow-y-auto">
        {loading ? (
          <div className="flex items-center justify-center h-20">
            <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-discord-primary"></div>
          </div>
        ) : error ? (
          <div className="p-4 text-discord-red text-sm">
            {error}
          </div>
        ) : (
          <div className="p-2">
            {sortedGroups.map(({ status, members: statusMembers }) => (
              <div key={status} className="mb-4">
                {/* Status group header */}
                <div className="px-2 mb-2">
                  <h4 className="text-xs font-semibold text-discord-channel uppercase tracking-wider">
                    {getStatusLabel(status)} — {statusMembers.length}
                  </h4>
                </div>

                {/* Members in this status group */}
                <div className="space-y-1">
                  {statusMembers.map((member) => (
                    <Tooltip
                      key={member.id}
                      content={`${member.profiles.username}#${member.profiles.discriminator}`}
                    >
                      <div className="flex items-center px-2 py-1 rounded hover:bg-discord-hover-bg cursor-pointer transition-colors group">
                        {/* Avatar with status */}
                        <div className="relative mr-3">
                          <div className="w-8 h-8 rounded-full bg-discord-primary flex items-center justify-center text-white text-sm font-medium">
                            {member.profiles.avatar}
                          </div>
                          <div className="absolute -bottom-0.5 -right-0.5">
                            <StatusIndicator 
                              status={member.profiles.status} 
                              size="sm"
                              className="border-2 border-discord-darker"
                            />
                          </div>
                        </div>

                        {/* Member info */}
                        <div className="flex-1 min-w-0">
                          <div className="text-sm font-medium text-discord-interactive-normal group-hover:text-discord-interactive-hover truncate">
                            {member.nickname || member.profiles.username}
                          </div>
                          {member.profiles.status === 'online' && member.profiles.customStatus && (
                            <div className="text-xs text-discord-text-muted truncate">
                              {member.profiles.customStatus}
                            </div>
                          )}
                        </div>

                        {/* Member actions (visible on hover) */}
                        <div className="opacity-0 group-hover:opacity-100 transition-opacity flex items-center space-x-1">
                          <Tooltip content="Send Message">
                            <button className="w-6 h-6 flex items-center justify-center text-discord-interactive-normal hover:text-discord-interactive-hover transition-colors">
                              <svg width="16" height="16" viewBox="0 0 24 24">
                                <path
                                  fill="currentColor"
                                  d="M4.79805 3C3.80445 3 2.99805 3.8055 2.99805 4.8V15.6C2.99805 16.5936 3.80445 17.4 4.79805 17.4H7.49805V21L11.098 17.4H19.198C20.1916 17.4 20.998 16.5936 20.998 15.6V4.8C20.998 3.8055 20.1916 3 19.198 3H4.79805ZM7.49805 7.2H16.498C16.9412 7.2 17.298 7.5568 17.298 8C17.298 8.4432 16.9412 8.8 16.498 8.8H7.49805C7.05485 8.8 6.69805 8.4432 6.69805 8C6.69805 7.5568 7.05485 7.2 7.49805 7.2ZM7.49805 10.4H13.298C13.7412 10.4 14.098 10.7568 14.098 11.2C14.098 11.6432 13.7412 12 13.298 12H7.49805C7.05485 12 6.69805 11.6432 6.69805 11.2C6.69805 10.7568 7.05485 10.4 7.49805 10.4Z"
                                />
                              </svg>
                            </button>
                          </Tooltip>

                          <Tooltip content="More">
                            <button className="w-6 h-6 flex items-center justify-center text-discord-interactive-normal hover:text-discord-interactive-hover transition-colors">
                              <svg width="16" height="16" viewBox="0 0 24 24">
                                <path
                                  fill="currentColor"
                                  d="M12 8C13.1 8 14 7.1 14 6C14 4.9 13.1 4 12 4C10.9 4 10 4.9 10 6C10 7.1 10.9 8 12 8ZM12 10C10.9 10 10 10.9 10 12C10 13.1 10.9 14 12 14C13.1 14 14 13.1 14 12C14 10.9 13.1 10 12 10ZM12 16C10.9 16 10 16.9 10 18C10 19.1 10.9 20 12 20C13.1 20 14 19.1 14 18C14 16.9 13.1 16 12 16Z"
                                />
                              </svg>
                            </button>
                          </Tooltip>
                        </div>
                      </div>
                    </Tooltip>
                  ))}
                </div>
              </div>
            ))}

            {members.length === 0 && !loading && (
              <div className="p-4 text-center text-discord-text-muted">
                <p className="text-sm">No members found</p>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Footer with online count */}
      <div className="p-2 border-t border-discord-separator">
        <div className="text-xs text-discord-text-muted">
          {getTotalOnlineCount()} of {members.length} members online
        </div>
      </div>
    </div>
  );
};

export default MemberList;
