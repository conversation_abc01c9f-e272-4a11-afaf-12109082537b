import React, { useState, useRef, useEffect } from 'react';
import { Server } from '../../types';

interface ServerHeaderProps {
  server: Server | null;
  onCreateChannel?: () => void;
  onInvitePeople?: () => void;
  onServerSettings?: () => void;
  onLeaveServer?: () => void;
}

const ServerHeader: React.FC<ServerHeaderProps> = ({
  server,
  onCreateChannel,
  onInvitePeople,
  onServerSettings,
  onLeaveServer
}) => {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleDropdownToggle = () => {
    setIsDropdownOpen(!isDropdownOpen);
  };

  return (
    <div className="relative">
      <div
        className="h-12 px-4 flex items-center justify-between border-b border-discord-separator bg-discord-darker cursor-pointer hover:bg-discord-hover-bg transition-colors"
        onClick={handleDropdownToggle}
      >
        <div className="flex items-center flex-1 min-w-0">
          <h2 className="font-semibold text-white truncate">
            {server?.name || 'Loading...'}
          </h2>
          
          {/* Boost level indicator */}
          {/* TODO: Add boost level logic */}
        </div>
        
        <div className="flex items-center">
          <svg
            width="18"
            height="18"
            viewBox="0 0 18 18"
            className={`text-discord-interactive-normal transition-transform duration-200 ${
              isDropdownOpen ? 'rotate-180' : ''
            }`}
          >
            <path
              fill="currentColor"
              d="M5.20711 7.20711C4.81658 6.81658 4.18342 6.81658 3.79289 7.20711C3.40237 7.59763 3.40237 8.23079 3.79289 8.62132L8.79289 13.6213C9.18342 14.0118 9.81658 14.0118 10.2071 13.6213L15.2071 8.62132C15.5976 8.23079 15.5976 7.59763 15.2071 7.20711C14.8166 6.81658 14.1834 6.81658 13.7929 7.20711L9.5 11.5L5.20711 7.20711Z"
            />
          </svg>
        </div>
      </div>

      {/* Dropdown menu */}
      {isDropdownOpen && (
        <div
          ref={dropdownRef}
          className="absolute top-full left-0 right-0 bg-discord-context-menu-bg border border-discord-separator rounded-md shadow-lg z-50 py-1.5 mx-2"
        >
          <button
            onClick={() => {
              onInvitePeople?.();
              setIsDropdownOpen(false);
            }}
            className="w-full px-3 py-2 text-left text-sm text-discord-interactive-normal hover:text-discord-interactive-hover hover:bg-discord-primary flex items-center"
          >
            <span className="mr-3 text-discord-primary">
              <svg width="16" height="16" viewBox="0 0 24 24">
                <path
                  fill="currentColor"
                  d="M12 2C13.1046 2 14 2.89543 14 4C14 5.10457 13.1046 6 12 6C10.8954 6 10 5.10457 10 4C10 2.89543 10.8954 2 12 2ZM12 8C15.3137 8 18 10.6863 18 14V16C18 17.1046 17.1046 18 16 18H8C6.89543 18 6 17.1046 6 16V14C6 10.6863 8.68629 8 12 8ZM12 10C9.79086 10 8 11.7909 8 14V16H16V14C16 11.7909 14.2091 10 12 10ZM18 12C19.1046 12 20 12.8954 20 14C20 15.1046 19.1046 16 18 16C16.8954 16 16 15.1046 16 14C16 12.8954 16.8954 12 18 12ZM6 12C7.10457 12 8 12.8954 8 14C8 15.1046 7.10457 16 6 16C4.89543 16 4 15.1046 4 14C4 12.8954 4.89543 12 6 12Z"
                />
              </svg>
            </span>
            Invite People
          </button>

          <div className="h-px bg-discord-separator my-1.5 mx-2" />

          <button
            onClick={() => {
              onServerSettings?.();
              setIsDropdownOpen(false);
            }}
            className="w-full px-3 py-2 text-left text-sm text-discord-interactive-normal hover:text-discord-interactive-hover hover:bg-discord-primary flex items-center"
          >
            <span className="mr-3">
              <svg width="16" height="16" viewBox="0 0 24 24">
                <path
                  fill="currentColor"
                  d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.8,11.69,4.8,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"
                />
              </svg>
            </span>
            Server Settings
          </button>

          <button
            onClick={() => {
              onCreateChannel?.();
              setIsDropdownOpen(false);
            }}
            className="w-full px-3 py-2 text-left text-sm text-discord-interactive-normal hover:text-discord-interactive-hover hover:bg-discord-primary flex items-center"
          >
            <span className="mr-3">
              <svg width="16" height="16" viewBox="0 0 24 24">
                <path
                  fill="currentColor"
                  d="M12 2C13.1046 2 14 2.89543 14 4V10H20C21.1046 10 22 10.8954 22 12C22 13.1046 21.1046 14 20 14H14V20C14 21.1046 13.1046 22 12 22C10.8954 22 10 21.1046 10 20V14H4C2.89543 14 2 13.1046 2 12C2 10.8954 2.89543 10 4 10H10V4C10 2.89543 10.8954 2 12 2Z"
                />
              </svg>
            </span>
            Create Channel
          </button>

          <div className="h-px bg-discord-separator my-1.5 mx-2" />

          <button
            onClick={() => {
              onLeaveServer?.();
              setIsDropdownOpen(false);
            }}
            className="w-full px-3 py-2 text-left text-sm text-discord-red hover:text-white hover:bg-discord-red flex items-center"
          >
            <span className="mr-3">
              <svg width="16" height="16" viewBox="0 0 24 24">
                <path
                  fill="currentColor"
                  d="M10.418 12L8.7 10.282C8.31 9.892 8.31 9.262 8.7 8.872C9.09 8.482 9.72 8.482 10.11 8.872L12.536 11.298C12.926 11.688 12.926 12.318 12.536 12.708L10.11 15.134C9.72 15.524 9.09 15.524 8.7 15.134C8.31 14.744 8.31 14.114 8.7 13.724L10.418 12ZM21 12C21 16.97 16.97 21 12 21C7.03 21 3 16.97 3 12C3 7.03 7.03 3 12 3C16.97 3 21 7.03 21 12ZM19 12C19 8.13 15.87 5 12 5C8.13 5 5 8.13 5 12C5 15.87 8.13 19 12 19C15.87 19 19 15.87 19 12Z"
                />
              </svg>
            </span>
            Leave Server
          </button>
        </div>
      )}
    </div>
  );
};

export default ServerHeader;
