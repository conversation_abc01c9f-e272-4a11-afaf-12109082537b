import React, { useState, useEffect, useRef } from 'react';
import { Message } from '../types';
import { getChannelMessages, sendMessage, subscribeToChannelMessages } from '../services/messageService';
import { getChannel } from '../services/channelService';
import { useAuth } from '../context/AuthContext';
import ChatHeader from './ui/ChatHeader';
import MessageInput from './ui/MessageInput';
import MessageComponent from './ui/MessageComponent';

interface ChatProps {
  channelId: string;
}

const Chat: React.FC<ChatProps> = ({ channelId }) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [channel, setChannel] = useState<any>(null);
  const [memberListVisible, setMemberListVisible] = useState(true);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const { currentUser } = useAuth();

  // Fetch channel info and messages
  useEffect(() => {
    const fetchChannelAndMessages = async () => {
      setLoading(true);
      setError('');

      try {
        // Get channel info
        const channelData = await getChannel(channelId);
        if (channelData) {
          setChannel(channelData);
        }

        // Get messages
        const messagesData = await getChannelMessages(channelId);
        setMessages(messagesData);
      } catch (err) {
        console.error('Error fetching channel data:', err);
        setError('Failed to load channel data. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchChannelAndMessages();
  }, [channelId]);

  // Subscribe to new messages
  useEffect(() => {
    const unsubscribe = subscribeToChannelMessages(channelId, (newMessage) => {
      setMessages(prevMessages => [...prevMessages, newMessage]);
    });

    return () => {
      unsubscribe();
    };
  }, [channelId]);

  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Handle sending a message
  const handleSendMessage = async (content: string) => {
    if (!content.trim() || !currentUser) return;

    try {
      await sendMessage(channelId, content);
    } catch (err) {
      console.error('Error sending message:', err);
      setError('Failed to send message. Please try again.');
    }
  };

  // Handle file upload
  const handleFileUpload = (files: FileList) => {
    // TODO: Implement file upload
    console.log('Files to upload:', files);
  };

  // Handle typing indicator
  const handleTyping = () => {
    // TODO: Implement typing indicator
    console.log('User is typing...');
  };

  // Message actions
  const handleReply = (message: Message) => {
    // TODO: Implement reply functionality
    console.log('Reply to message:', message);
  };

  const handleEdit = (message: Message) => {
    // TODO: Implement edit functionality
    console.log('Edit message:', message);
  };

  const handleDelete = (message: Message) => {
    // TODO: Implement delete functionality
    console.log('Delete message:', message);
  };

  const handleReact = (message: Message, emoji: string) => {
    // TODO: Implement reaction functionality
    console.log('React to message:', message, 'with:', emoji);
  };

  // Format timestamp
  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // Format date for message groups
  const formatMessageDate = (timestamp: string) => {
    const date = new Date(timestamp);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) {
      return 'Today';
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Yesterday';
    } else {
      return date.toLocaleDateString(undefined, {
        month: 'long',
        day: 'numeric',
        year: date.getFullYear() !== today.getFullYear() ? 'numeric' : undefined
      });
    }
  };

  // Group messages by author and date
  const groupedMessages = messages.reduce((groups: any[], message, index) => {
    const previousMessage = messages[index - 1];

    // Check if this is a new day compared to the previous message
    if (index === 0 || formatMessageDate(message.timestamp) !== formatMessageDate(previousMessage.timestamp)) {
      groups.push({
        type: 'date-divider',
        date: formatMessageDate(message.timestamp),
        id: `date-${message.timestamp}`
      });
    }

    // Check if this message should be grouped with the previous one
    const shouldGroup =
      previousMessage &&
      message.author.id === previousMessage.author.id &&
      new Date(message.timestamp).getTime() - new Date(previousMessage.timestamp).getTime() < 5 * 60 * 1000; // 5 minutes

    if (shouldGroup) {
      // Add to the previous group
      const lastGroup = groups[groups.length - 1];
      if (lastGroup.type === 'message-group') {
        lastGroup.messages.push(message);
      } else {
        // Create a new group
        groups.push({
          type: 'message-group',
          author: message.author,
          messages: [message],
          id: `group-${message.id}`
        });
      }
    } else {
      // Create a new group
      groups.push({
        type: 'message-group',
        author: message.author,
        messages: [message],
        id: `group-${message.id}`
      });
    }

    return groups;
  }, []);

  if (loading) {
    return (
      <div className="flex-1 flex items-center justify-center bg-discord-bg">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-discord-primary"></div>
      </div>
    );
  }

  // Group messages for better display
  const groupedMessages = messages.reduce((groups: any[], message, index) => {
    const previousMessage = messages[index - 1];

    // Check if this is a new day compared to the previous message
    if (index === 0 || formatMessageDate(message.timestamp) !== formatMessageDate(previousMessage.timestamp)) {
      groups.push({
        type: 'date-divider',
        date: formatMessageDate(message.timestamp),
        id: `date-${message.timestamp}`
      });
    }

    // Check if this message should be grouped with the previous one
    const shouldGroup =
      previousMessage &&
      message.author.id === previousMessage.author.id &&
      new Date(message.timestamp).getTime() - new Date(previousMessage.timestamp).getTime() < 5 * 60 * 1000; // 5 minutes

    groups.push({
      type: 'message',
      message,
      isGrouped: shouldGroup,
      id: message.id
    });

    return groups;
  }, []);

  return (
    <div className="flex-1 flex flex-col bg-discord-bg">
      {/* Channel header */}
      <ChatHeader
        channel={channel}
        onToggleMemberList={() => setMemberListVisible(!memberListVisible)}
        onStartThread={() => console.log('Start thread')}
        onToggleNotifications={() => console.log('Toggle notifications')}
        onShowPinnedMessages={() => console.log('Show pinned messages')}
        onSearch={() => console.log('Search')}
        onShowInbox={() => console.log('Show inbox')}
        onShowHelp={() => console.log('Show help')}
        memberListVisible={memberListVisible}
      />

      {/* Messages area */}
      <div
        ref={messagesContainerRef}
        className="flex-1 overflow-y-auto"
      >
        {error && (
          <div className="p-4 m-4 text-white bg-discord-red rounded-md">
            {error}
          </div>
        )}

        {groupedMessages.length === 0 && !loading && (
          <div className="flex flex-col items-center justify-center h-full text-discord-interactive-muted px-4">
            <div className="w-16 h-16 bg-discord-interactive-muted rounded-full flex items-center justify-center mb-4">
              <svg width="40" height="40" viewBox="0 0 24 24" className="text-discord-bg">
                <path
                  fill="currentColor"
                  d="M5.88657 21C5.57547 21 5.3399 20.7189 5.39427 20.4126L6.00001 17H2.59511C2.28449 17 2.04905 16.7198 2.10259 16.4138L2.27759 15.4138C2.31946 15.1746 2.52722 15 2.77011 15H6.35001L7.41001 9H4.00511C3.69449 9 3.45905 8.71977 3.51259 8.41381L3.68759 7.41381C3.72946 7.17456 3.93722 7 4.18011 7H7.76001L8.39677 3.41262C8.43914 3.17391 8.64664 3 8.88907 3H9.87344C10.1845 3 10.4201 3.28107 10.3657 3.58738L9.76001 7H15.76L16.3968 3.41262C16.4391 3.17391 16.6466 3 16.8891 3H17.8734C18.1845 3 18.4201 3.28107 18.3657 3.58738L17.76 7H21.1649C21.4755 7 21.711 7.28023 21.6574 7.58619L21.4824 8.58619C21.4406 8.82544 21.2328 9 20.9899 9H17.41L16.35 15H19.7549C20.0655 15 20.301 15.2802 20.2474 15.5862L20.0724 16.5862C20.0306 16.8254 19.8228 17 19.5799 17H16L15.3632 20.5874C15.3209 20.8261 15.1134 21 14.8709 21H13.8866C13.5755 21 13.3399 20.7189 13.3943 20.4126L14 17H8.00001L7.36325 20.5874C7.32088 20.8261 7.11337 21 6.87094 21H5.88657ZM9.41001 9L8.35001 15H14.35L15.41 9H9.41001Z"
                />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-discord-header-primary mb-2">
              Welcome to #{channel?.name || 'this channel'}!
            </h3>
            <p className="text-center">
              This is the start of the #{channel?.name || 'channel'} channel.
              {channel?.topic && (
                <>
                  <br />
                  <span className="text-discord-text-muted">{channel.topic}</span>
                </>
              )}
            </p>
          </div>
        )}

        {groupedMessages.map((item) => {
          if (item.type === 'date-divider') {
            return (
              <div key={item.id} className="flex items-center my-6 mx-4">
                <div className="flex-1 h-px bg-discord-separator"></div>
                <div className="px-4 text-xs font-semibold text-discord-text-muted">
                  {item.date}
                </div>
                <div className="flex-1 h-px bg-discord-separator"></div>
              </div>
            );
          }

          return (
            <MessageComponent
              key={item.id}
              message={item.message}
              isGrouped={item.isGrouped}
              onReply={handleReply}
              onEdit={handleEdit}
              onDelete={handleDelete}
              onReact={handleReact}
              currentUserId={currentUser?.id}
            />
          );
        })}

        <div ref={messagesEndRef} />
      </div>

      {/* Message input */}
      <MessageInput
        channelName={channel?.name}
        onSendMessage={handleSendMessage}
        onFileUpload={handleFileUpload}
        onTyping={handleTyping}
        disabled={loading}
      />
    </div>
  );
};

export default Chat;
