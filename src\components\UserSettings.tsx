import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { updateProfile } from '../services/profileService';

interface UserSettingsProps {
  isOpen: boolean;
  onClose: () => void;
}

const UserSettings: React.FC<UserSettingsProps> = ({ isOpen, onClose }) => {
  const { currentUser } = useAuth();
  const [username, setUsername] = useState('');
  const [customStatus, setCustomStatus] = useState('');
  const [status, setStatus] = useState<'online' | 'idle' | 'dnd' | 'offline'>('online');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  useEffect(() => {
    if (currentUser) {
      setUsername(currentUser.username);
      setStatus(currentUser.status);
      setCustomStatus(currentUser.customStatus || '');
    }
  }, [currentUser]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!currentUser) return;
    
    setIsLoading(true);
    setError('');
    setSuccess('');
    
    try {
      const result = await updateProfile(currentUser.id, {
        username,
        status,
        custom_status: customStatus,
      });
      
      if (result) {
        setSuccess('Profile updated successfully!');
      } else {
        setError('Failed to update profile. Please try again.');
      }
    } catch (err) {
      setError('An error occurred. Please try again.');
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50">
      <div className="bg-discord-light-bg rounded-lg w-full max-w-md p-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-white">User Settings</h2>
          <button
            onClick={onClose}
            className="text-discord-muted hover:text-white"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {error && (
          <div className="p-3 mb-4 text-white bg-discord-red rounded-md">
            {error}
          </div>
        )}

        {success && (
          <div className="p-3 mb-4 text-white bg-discord-green rounded-md">
            {success}
          </div>
        )}

        <form onSubmit={handleSubmit}>
          <div className="mb-4">
            <label htmlFor="username" className="block text-sm font-medium text-discord-channel mb-1">
              USERNAME
            </label>
            <input
              id="username"
              type="text"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              className="w-full px-3 py-2 text-white bg-discord-dark-bg border border-discord-separator rounded-md focus:outline-none focus:ring-2 focus:ring-discord-primary"
              required
            />
          </div>

          <div className="mb-4">
            <label htmlFor="status" className="block text-sm font-medium text-discord-channel mb-1">
              STATUS
            </label>
            <select
              id="status"
              value={status}
              onChange={(e) => setStatus(e.target.value as 'online' | 'idle' | 'dnd' | 'offline')}
              className="w-full px-3 py-2 text-white bg-discord-dark-bg border border-discord-separator rounded-md focus:outline-none focus:ring-2 focus:ring-discord-primary"
            >
              <option value="online">Online</option>
              <option value="idle">Idle</option>
              <option value="dnd">Do Not Disturb</option>
              <option value="offline">Invisible</option>
            </select>
          </div>

          <div className="mb-6">
            <label htmlFor="customStatus" className="block text-sm font-medium text-discord-channel mb-1">
              CUSTOM STATUS
            </label>
            <input
              id="customStatus"
              type="text"
              value={customStatus}
              onChange={(e) => setCustomStatus(e.target.value)}
              placeholder="What's happening?"
              className="w-full px-3 py-2 text-white bg-discord-dark-bg border border-discord-separator rounded-md focus:outline-none focus:ring-2 focus:ring-discord-primary"
            />
          </div>

          <div className="flex justify-end">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-white bg-discord-dark-bg rounded-md mr-2 hover:bg-opacity-80"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="px-4 py-2 text-white bg-discord-primary rounded-md hover:bg-opacity-80 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-discord-primary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? 'Saving...' : 'Save Changes'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default UserSettings;
