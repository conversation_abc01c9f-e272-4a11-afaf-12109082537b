document.addEventListener('DOMContentLoaded', function() {
    // Server selection
    const serverIcons = document.querySelectorAll('.server-icon');
    serverIcons.forEach(icon => {
        icon.addEventListener('click', function() {
            // Remove active class from all server icons
            serverIcons.forEach(i => i.classList.remove('active'));
            // Add active class to clicked server icon
            this.classList.add('active');
            
            // Update channel sidebar based on selected server
            const serverId = this.getAttribute('data-server');
            if (serverId) {
                document.querySelector('.server-header h3').textContent = 
                    serverId.charAt(0).toUpperCase() + serverId.slice(1) + ' Server';
            } else {
                document.querySelector('.server-header h3').textContent = 'Discord Clone';
            }
        });
    });

    // Channel selection
    const channels = document.querySelectorAll('.channel');
    channels.forEach(channel => {
        channel.addEventListener('click', function() {
            // Remove active class from all channels
            channels.forEach(c => c.classList.remove('active'));
            // Add active class to clicked channel
            this.classList.add('active');
            
            // Update chat header with channel name
            const channelName = this.querySelector('.channel-name').textContent;
            document.querySelector('.chat-header .channel-name').textContent = channelName;
            
            // Clear unread indicators and mentions
            const unreadIndicator = this.querySelector('.unread-indicator');
            const mentionIndicator = this.querySelector('.mention-indicator');
            
            if (unreadIndicator) {
                unreadIndicator.remove();
            }
            
            if (mentionIndicator) {
                mentionIndicator.remove();
            }
        });
    });

    // Category collapse/expand
    const categoryHeaders = document.querySelectorAll('.category-header');
    categoryHeaders.forEach(header => {
        header.addEventListener('click', function() {
            const category = this.parentElement;
            const channels = category.querySelectorAll('.channel');
            const icon = this.querySelector('i');
            
            // Toggle channels visibility
            channels.forEach(channel => {
                if (channel.style.display === 'none') {
                    channel.style.display = 'flex';
                    icon.className = 'fas fa-chevron-down';
                } else {
                    channel.style.display = 'none';
                    icon.className = 'fas fa-chevron-right';
                }
            });
        });
    });

    // Message input functionality
    const messageInput = document.querySelector('.message-input input');
    messageInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            const messageText = this.value.trim();
            
            if (messageText) {
                // Create new message element
                const messagesContainer = document.querySelector('.chat-messages');
                const newMessage = document.createElement('div');
                newMessage.className = 'message';
                
                // Get current time
                const now = new Date();
                const hours = now.getHours();
                const minutes = now.getMinutes();
                const ampm = hours >= 12 ? 'PM' : 'AM';
                const formattedHours = hours % 12 || 12;
                const formattedMinutes = minutes < 10 ? '0' + minutes : minutes;
                const timeString = `Today at ${formattedHours}:${formattedMinutes} ${ampm}`;
                
                // Set message content
                newMessage.innerHTML = `
                    <div class="message-avatar">
                        <img src="img/avatar.png" alt="User Avatar" onerror="this.src='https://via.placeholder.com/40'">
                    </div>
                    <div class="message-content">
                        <div class="message-header">
                            <div class="message-author">Username</div>
                            <div class="message-time">${timeString}</div>
                        </div>
                        <div class="message-text">${formatMessage(messageText)}</div>
                    </div>
                `;
                
                // Add message to chat
                messagesContainer.appendChild(newMessage);
                
                // Scroll to bottom
                messagesContainer.scrollTop = messagesContainer.scrollHeight;
                
                // Clear input
                this.value = '';
            }
        }
    });

    // Format message with markdown-like syntax
    function formatMessage(text) {
        // Bold
        text = text.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
        
        // Italic
        text = text.replace(/\*(.*?)\*/g, '<em>$1</em>');
        
        // Strikethrough
        text = text.replace(/~~(.*?)~~/g, '<del>$1</del>');
        
        // Code blocks
        text = text.replace(/`(.*?)`/g, '<code>$1</code>');
        
        // Links
        text = text.replace(/(https?:\/\/[^\s]+)/g, '<a href="$1">$1</a>');
        
        return text;
    }

    // Settings modal
    const settingsButton = document.querySelector('.user-controls .control-button:last-child');
    const settingsModal = document.querySelector('.settings-modal');
    const closeButton = document.querySelector('.close-button');
    
    settingsButton.addEventListener('click', function() {
        settingsModal.style.display = 'flex';
    });
    
    closeButton.addEventListener('click', function() {
        settingsModal.style.display = 'none';
    });
    
    // Close modal when clicking outside
    settingsModal.addEventListener('click', function(e) {
        if (e.target === this) {
            this.style.display = 'none';
        }
    });
    
    // Close modal with Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && settingsModal.style.display === 'flex') {
            settingsModal.style.display = 'none';
        }
    });

    // Settings categories
    const settingsCategories = document.querySelectorAll('.settings-category');
    settingsCategories.forEach(category => {
        category.addEventListener('click', function() {
            // Remove active class from all categories
            settingsCategories.forEach(c => c.classList.remove('active'));
            // Add active class to clicked category
            this.classList.add('active');
            
            // Update settings header
            const categoryName = this.textContent;
            document.querySelector('.settings-header h2').textContent = categoryName;
            
            // Update settings content (would be implemented with actual settings)
        });
    });

    // User controls
    const muteButton = document.querySelector('.user-controls .control-button:first-child');
    muteButton.addEventListener('click', function() {
        this.classList.toggle('active');
        const icon = this.querySelector('i');
        
        if (this.classList.contains('active')) {
            icon.className = 'fas fa-microphone-slash';
        } else {
            icon.className = 'fas fa-microphone';
        }
    });
    
    const deafenButton = document.querySelector('.user-controls .control-button:nth-child(2)');
    deafenButton.addEventListener('click', function() {
        this.classList.toggle('active');
        const icon = this.querySelector('i');
        
        if (this.classList.contains('active')) {
            icon.className = 'fas fa-headphones-alt';
            // Also mute when deafened
            muteButton.classList.add('active');
            muteButton.querySelector('i').className = 'fas fa-microphone-slash';
        } else {
            icon.className = 'fas fa-headphones';
        }
    });

    // Member list toggle
    const membersToggle = document.querySelector('.header-controls .control-button:nth-child(3)');
    membersToggle.addEventListener('click', function() {
        const memberList = document.querySelector('.member-list');
        
        if (memberList.style.display === 'none') {
            memberList.style.display = 'block';
        } else {
            memberList.style.display = 'none';
        }
    });

    // Context menus
    const addContextMenu = (selector, menuItems) => {
        const elements = document.querySelectorAll(selector);
        
        elements.forEach(element => {
            element.addEventListener('contextmenu', function(e) {
                e.preventDefault();
                
                // Remove any existing context menus
                const existingMenu = document.querySelector('.context-menu');
                if (existingMenu) {
                    existingMenu.remove();
                }
                
                // Create context menu
                const contextMenu = document.createElement('div');
                contextMenu.className = 'context-menu';
                contextMenu.style.position = 'absolute';
                contextMenu.style.left = `${e.pageX}px`;
                contextMenu.style.top = `${e.pageY}px`;
                contextMenu.style.backgroundColor = '#18191C';
                contextMenu.style.borderRadius = '4px';
                contextMenu.style.padding = '6px 0';
                contextMenu.style.boxShadow = '0 0 0 1px rgba(32,34,37,.6), 0 2px 10px 0 rgba(0,0,0,.2)';
                contextMenu.style.zIndex = '1000';
                
                // Add menu items
                menuItems.forEach(item => {
                    const menuItem = document.createElement('div');
                    menuItem.className = 'context-menu-item';
                    menuItem.textContent = item;
                    menuItem.style.padding = '6px 8px';
                    menuItem.style.margin = '2px 0';
                    menuItem.style.color = '#DCDDDE';
                    menuItem.style.fontSize = '14px';
                    menuItem.style.cursor = 'pointer';
                    
                    menuItem.addEventListener('mouseover', function() {
                        this.style.backgroundColor = '#4F545C';
                        this.style.color = '#FFFFFF';
                    });
                    
                    menuItem.addEventListener('mouseout', function() {
                        this.style.backgroundColor = 'transparent';
                        this.style.color = '#DCDDDE';
                    });
                    
                    menuItem.addEventListener('click', function() {
                        // Handle menu item click (would implement actual functionality)
                        alert(`${item} clicked on ${element.textContent || 'element'}`);
                        contextMenu.remove();
                    });
                    
                    contextMenu.appendChild(menuItem);
                });
                
                document.body.appendChild(contextMenu);
                
                // Close context menu when clicking outside
                document.addEventListener('click', function closeMenu() {
                    contextMenu.remove();
                    document.removeEventListener('click', closeMenu);
                });
            });
        });
    };
    
    // Add context menus
    addContextMenu('.server-icon', ['Mark As Read', 'Notification Settings', 'Privacy Settings', 'Leave Server']);
    addContextMenu('.channel', ['Mark As Read', 'Mute Channel', 'Invite People', 'Edit Channel', 'Delete Channel']);
    addContextMenu('.message', ['Reply', 'Pin Message', 'Edit Message', 'Delete Message', 'Copy ID']);
    addContextMenu('.member', ['Message', 'Call', 'Add Friend', 'Profile', 'Mute', 'Block']);
});
