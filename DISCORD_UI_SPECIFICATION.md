# Discord UI Complete Technical Specification

## Overview
This document provides a comprehensive analysis of <PERSON><PERSON>'s user interface, documenting every button, function, and interactive element with exact specifications for implementation.

## Color Palette
```css
/* Primary Colors */
--discord-blurple: #5865F2;
--discord-green: #57F287;
--discord-yellow: #FEE75C;
--discord-fuchsia: #EB459E;
--discord-red: #ED4245;

/* Background Colors */
--discord-dark: #36393F;
--discord-darker: #2F3136;
--discord-darkest: #202225;
--discord-light: #40444B;
--discord-lighter: #484C52;

/* Text Colors */
--discord-text-normal: #DCDDDE;
--discord-text-muted: #72767D;
--discord-text-link: #00AFF4;
--discord-header-primary: #FFFFFF;
--discord-header-secondary: #B9BBBE;

/* Interactive Colors */
--discord-interactive-normal: #B9BBBE;
--discord-interactive-hover: #DCDDDE;
--discord-interactive-active: #FFFFFF;
--discord-interactive-muted: #4F545C;

/* Status Colors */
--discord-status-online: #43B581;
--discord-status-idle: #FAA61A;
--discord-status-dnd: #F04747;
--discord-status-offline: #747F8D;
```

## Layout Structure

### 1. Server Sidebar (72px width)
**Position:** Fixed left, full height
**Background:** #202225
**Components:**
- Home button (48x48px, rounded-full on hover)
- Server separator (32x2px, #36393F)
- Server list (48x48px each, 8px spacing)
- Add server button (48x48px, green on hover)
- Explore public servers button (48x48px)
- Download apps button (48x48px)

### 2. Channel Sidebar (240px width)
**Position:** Left of main content
**Background:** #2F3136
**Components:**
- Server header (240x48px)
  - Server name (truncated)
  - Dropdown arrow
  - Notification settings
- Channel categories
  - Category header (collapsed/expanded state)
  - Text channels (#icon, name, notification badge)
  - Voice channels (speaker icon, name, user count)
  - Create channel button (+)
- User area (240x52px)
  - Avatar (32x32px)
  - Username and discriminator
  - Status indicator (12x12px)
  - Mute button
  - Deafen button
  - Settings button

### 3. Main Content Area (flexible width)
**Components:**
- Channel header (full width x 48px)
  - Channel name and topic
  - Thread button
  - Notification bell
  - Pinned messages
  - Member list toggle
  - Search
  - Inbox
  - Help
- Message area (scrollable)
  - Message groups
  - Typing indicators
  - Jump to present
- Message input area (full width x variable height)
  - File upload button
  - Text input with formatting
  - Gift button
  - GIF button
  - Emoji button
  - Send button (when text present)

### 4. Member List (240px width, collapsible)
**Position:** Right sidebar
**Background:** #2F3136
**Components:**
- Online members count
- Role categories
- Member list with status indicators
- Member context menus

## Detailed Component Specifications

### Server List Components

#### Home Button
- **Size:** 48x48px
- **Default:** Rounded 16px, Discord logo
- **Hover:** Rounded 12px, background #5865F2
- **Active:** Rounded 12px, white pill indicator (4x40px)
- **Tooltip:** "Direct Messages"

#### Server Icons
- **Size:** 48x48px
- **Default:** Rounded 16px
- **Hover:** Rounded 12px
- **Active:** Rounded 12px, white pill indicator
- **Unread:** White dot indicator (8x8px)
- **Mention:** Red badge with count

#### Add Server Button
- **Size:** 48x48px
- **Default:** Rounded 16px, + icon, #36393F background
- **Hover:** Rounded 12px, #43B581 background
- **Tooltip:** "Add a Server"
- **Function:** Opens server creation/join modal

#### Explore Public Servers
- **Size:** 48x48px
- **Default:** Compass icon
- **Hover:** Rounded 12px, #43B581 background
- **Tooltip:** "Explore Discoverable Servers"

### Channel Sidebar Components

#### Server Header
- **Height:** 48px
- **Padding:** 12px 16px
- **Border:** 1px solid #202225 (bottom)
- **Hover:** Background #34373C
- **Components:**
  - Server name (font-weight: 600, truncated)
  - Dropdown chevron
  - Boost level indicator (if applicable)

#### Channel Categories
- **Height:** 24px per category header
- **Padding:** 16px 8px 0 16px
- **Font:** 12px, uppercase, #8E9297
- **Hover:** #DCDDDE
- **Collapsed/Expanded state with chevron

#### Text Channels
- **Height:** 32px
- **Padding:** 1px 8px
- **Border-radius:** 4px
- **Default:** #72767D text
- **Hover:** #DCDDDE text, #393C43 background
- **Active:** #FFFFFF text, #42464D background
- **Unread:** #FFFFFF text, white dot
- **Mention:** Red badge with count

#### Voice Channels
- **Height:** 32px
- **Components:**
  - Speaker icon
  - Channel name
  - User limit indicator
  - Connected users (avatars)
  - User count

### Message Area Components

#### Message Groups
- **Padding:** 0.125rem 1rem
- **Hover:** Background #32353B
- **Components:**
  - Avatar (40x40px, left margin)
  - Username (font-weight: 500, color varies by role)
  - Timestamp (12px, #72767D)
  - Message content
  - Reactions
  - Reply/Edit/Delete buttons (on hover)

#### Message Input
- **Background:** #40444B
- **Border-radius:** 8px
- **Padding:** 11px 16px
- **Components:**
  - Upload button (24x24px)
  - Text input (placeholder: "Message #channel-name")
  - Gift button (24x24px)
  - GIF button (24x24px)
  - Emoji button (24x24px)
  - Send button (appears when typing)

### User Area Components

#### User Panel
- **Height:** 52px
- **Background:** #292B2F
- **Padding:** 8px
- **Components:**
  - Avatar (32x32px) with status indicator
  - Username/discriminator
  - Mute button (32x32px)
  - Deafen button (32x32px)
  - Settings button (32x32px)

## Interactive States

### Button States
1. **Default:** Base appearance
2. **Hover:** Slight color change, possible background
3. **Active/Pressed:** Darker shade
4. **Disabled:** Reduced opacity (0.6)
5. **Loading:** Spinner animation

### Tooltip Specifications
- **Background:** #18191C
- **Text:** #DCDDDE
- **Border-radius:** 5px
- **Padding:** 8px 12px
- **Font-size:** 14px
- **Font-weight:** 500
- **Delay:** 1000ms
- **Animation:** Fade in 100ms

### Context Menu Specifications
- **Background:** #18191C
- **Border:** 1px solid #2F3136
- **Border-radius:** 6px
- **Box-shadow:** 0 8px 16px rgba(0,0,0,0.24)
- **Padding:** 6px 8px
- **Min-width:** 188px

## Keyboard Shortcuts
- **Ctrl+K:** Quick switcher
- **Ctrl+Shift+I:** Toggle developer tools
- **Ctrl+R:** Reload
- **Ctrl+Shift+R:** Hard reload
- **Ctrl+Plus:** Zoom in
- **Ctrl+Minus:** Zoom out
- **Ctrl+0:** Reset zoom
- **Escape:** Close modals/menus
- **Tab:** Navigate between elements
- **Enter:** Send message
- **Shift+Enter:** New line in message
- **Up Arrow:** Edit last message
- **Ctrl+A:** Select all text
- **Ctrl+Z:** Undo
- **Ctrl+Y:** Redo

## Animation Specifications
- **Hover transitions:** 100ms ease
- **Button press:** 50ms ease
- **Modal open/close:** 200ms ease-out
- **Tooltip fade:** 100ms ease
- **Loading spinners:** 1s linear infinite
- **Notification badges:** Scale animation on appear

## Advanced Component Specifications

### Modal Dialogs

#### Server Settings Modal
- **Size:** 1000x700px (responsive)
- **Background:** #36393F
- **Sidebar:** 218px width, #2F3136 background
- **Sections:**
  - Overview
  - Roles
  - Emoji
  - Stickers
  - Moderation
  - Audit Log
  - Integrations
  - Widget
  - Server Template
  - Delete Server

#### User Settings Modal
- **Size:** 1000x700px (responsive)
- **Sections:**
  - My Account
  - Privacy & Safety
  - Authorized Apps
  - Connections
  - Billing
  - Nitro
  - Server Boost
  - Subscriptions
  - Gift Inventory
  - Appearance
  - Accessibility
  - Voice & Video
  - Text & Images
  - Notifications
  - Keybinds
  - Language
  - Windows Settings
  - Streamer Mode
  - Advanced

### Right-Click Context Menus

#### Server Context Menu
- **Items:**
  - Invite People
  - Server Settings
  - Create Channel
  - Create Category
  - Server Boost
  - Notification Settings
  - Privacy Settings
  - Edit Server Profile
  - Hide Muted Channels
  - Leave Server

#### Channel Context Menu
- **Items:**
  - Mark As Read
  - Mute Channel
  - Notification Settings
  - Invite People
  - Edit Channel
  - Clone Channel
  - Create Invite
  - Copy Channel ID
  - Delete Channel

#### Message Context Menu
- **Items:**
  - Add Reaction
  - Reply
  - Copy Message Link
  - Mark Unread
  - Pin Message
  - Edit Message
  - Delete Message
  - Copy Text
  - Copy Message ID
  - Report Message

### Status Indicators

#### User Status
- **Online:** #43B581 circle
- **Idle:** #FAA61A crescent
- **Do Not Disturb:** #F04747 circle with line
- **Invisible/Offline:** #747F8D circle
- **Streaming:** #593695 circle
- **Custom Status:** Emoji + text

#### Server Features
- **Verified:** Checkmark badge
- **Partnered:** Partner badge
- **Boost Level:** Boost icon with level
- **Community:** Community badge

### Notification System

#### Notification Badges
- **Unread:** White dot (6x6px)
- **Mention:** Red circle with white text
- **Position:** Top-right of element
- **Animation:** Scale in from 0.8 to 1.0

#### Toast Notifications
- **Position:** Bottom-right corner
- **Size:** 300x80px
- **Background:** #2F3136
- **Border:** 1px solid #202225
- **Duration:** 4000ms
- **Animation:** Slide in from right

This specification serves as the foundation for implementing a pixel-perfect Discord clone with full functionality.
