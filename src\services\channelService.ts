import { supabase } from '../lib/supabase';
import { Channel } from '../types';

/**
 * Get all channels in a server
 */
export const getServerChannels = async (serverId: string): Promise<Channel[]> => {
  const { data, error } = await supabase
    .from('channels')
    .select(`
      id,
      server_id,
      name,
      type,
      topic,
      position,
      parent_id,
      created_at,
      updated_at
    `)
    .eq('server_id', serverId)
    .order('position');

  if (error) {
    console.error('Error fetching channels:', error);
    return [];
  }

  return data || [];
};

/**
 * Get a channel by ID
 */
export const getChannel = async (channelId: string): Promise<Channel | null> => {
  const { data, error } = await supabase
    .from('channels')
    .select(`
      id,
      server_id,
      name,
      type,
      topic,
      position,
      parent_id,
      created_at,
      updated_at
    `)
    .eq('id', channelId)
    .single();

  if (error) {
    console.error('Error fetching channel:', error);
    return null;
  }

  return data;
};

/**
 * Create a new channel
 */
export const createChannel = async (
  serverId: string,
  name: string,
  type: 'text' | 'voice' | 'announcement' | 'forum' | 'stage',
  options?: {
    topic?: string;
    position?: number;
    parentId?: string;
  }
): Promise<Channel | null> => {
  const { data, error } = await supabase
    .from('channels')
    .insert({
      server_id: serverId,
      name,
      type,
      topic: options?.topic,
      position: options?.position || 0,
      parent_id: options?.parentId
    })
    .select()
    .single();

  if (error) {
    console.error('Error creating channel:', error);
    return null;
  }

  return data;
};

/**
 * Update a channel
 */
export const updateChannel = async (
  channelId: string,
  updates: {
    name?: string;
    topic?: string;
    position?: number;
    parentId?: string;
  }
): Promise<Channel | null> => {
  const { data, error } = await supabase
    .from('channels')
    .update({
      name: updates.name,
      topic: updates.topic,
      position: updates.position,
      parent_id: updates.parentId
    })
    .eq('id', channelId)
    .select()
    .single();

  if (error) {
    console.error('Error updating channel:', error);
    return null;
  }

  return data;
};

/**
 * Delete a channel
 */
export const deleteChannel = async (channelId: string): Promise<boolean> => {
  const { error } = await supabase
    .from('channels')
    .delete()
    .eq('id', channelId);

  if (error) {
    console.error('Error deleting channel:', error);
    return false;
  }

  return true;
};

/**
 * Get all channels grouped by category
 */
export const getChannelsByCategory = async (serverId: string): Promise<{
  categories: Channel[];
  channels: Record<string, Channel[]>;
}> => {
  const { data, error } = await supabase
    .from('channels')
    .select(`
      id,
      server_id,
      name,
      type,
      topic,
      position,
      parent_id,
      created_at,
      updated_at
    `)
    .eq('server_id', serverId)
    .order('position');

  if (error) {
    console.error('Error fetching channels:', error);
    return { categories: [], channels: {} };
  }

  const categories = data.filter(channel => channel.type === 'category');
  const nonCategories = data.filter(channel => channel.type !== 'category');

  const channelsByCategory: Record<string, Channel[]> = {};

  // Add channels with no category to "uncategorized"
  const uncategorizedChannels = nonCategories.filter(channel => !channel.parent_id);
  if (uncategorizedChannels.length > 0) {
    channelsByCategory['uncategorized'] = uncategorizedChannels;
  }

  // Group channels by category
  categories.forEach(category => {
    const categoryChannels = nonCategories.filter(channel => channel.parent_id === category.id);
    if (categoryChannels.length > 0) {
      channelsByCategory[category.id] = categoryChannels;
    }
  });

  return {
    categories,
    channels: channelsByCategory
  };
};
