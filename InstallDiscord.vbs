Set objWShell = CreateObject("WScript.Shell")
strDesktop = objWShell.SpecialFolders("Desktop")
strCurrentDir = CreateObject("Scripting.FileSystemObject").GetParentFolderName(WScript.ScriptFullName)

' Create desktop shortcut
Set objShortcut = objWShell.CreateShortcut(strDesktop & "\Discord.lnk")
objShortcut.TargetPath = strCurrentDir & "\Discord.bat"
objShortcut.WorkingDirectory = strCurrentDir
objShortcut.Description = "Discord Clone Application"
objShortcut.Save

MsgBox "Discord shortcut created on your desktop. Double-click it to open Discord.", 64, "Discord Setup"

' Run Discord
objWShell.Run """" & strCurrentDir & "\Discord.bat" & """", 1, False
