import React from 'react';

// Mock data for server members
const mockMembers = [
  {
    id: '1',
    username: '<PERSON><PERSON><PERSON>',
    avatar: '👨',
    status: 'online',
    activity: 'Playing Minecraft',
    roles: ['Admin']
  },
  {
    id: '2',
    username: '<PERSON><PERSON><PERSON>',
    avatar: '👩',
    status: 'idle',
    activity: null,
    roles: ['Moderator']
  },
  {
    id: '3',
    username: '<PERSON><PERSON><PERSON><PERSON>',
    avatar: '🤖',
    status: 'online',
    activity: null,
    roles: ['<PERSON><PERSON>']
  },
  {
    id: '4',
    username: '<PERSON><PERSON><PERSON><PERSON>',
    avatar: '👨‍🦰',
    status: 'dnd',
    activity: 'Spotify: Imagine Dragons - Believer',
    roles: ['Member']
  },
  {
    id: '5',
    username: '<PERSON><PERSON><PERSON><PERSON>',
    avatar: '👩‍🦱',
    status: 'offline',
    activity: null,
    roles: ['Member']
  },
  {
    id: '6',
    username: '<PERSON><PERSON><PERSON><PERSON>',
    avatar: '👨‍🦲',
    status: 'online',
    activity: null,
    roles: ['Member']
  },
  {
    id: '7',
    username: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    avatar: '👩‍🦳',
    status: 'online',
    activity: 'Playing Valorant',
    roles: ['Member']
  }
];

interface MembersListProps {
  serverId?: string;
}

const MembersList: React.FC<MembersListProps> = ({ serverId }) => {
  // Group members by role
  const groupedMembers = mockMembers.reduce((groups: Record<string, any[]>, member) => {
    // Get the highest role (first in the array)
    const role = member.roles[0] || 'Member';
    
    if (!groups[role]) {
      groups[role] = [];
    }
    
    groups[role].push(member);
    return groups;
  }, {});
  
  // Sort roles by priority
  const rolePriority = ['Admin', 'Moderator', 'Bot', 'Member'];
  const sortedRoles = Object.keys(groupedMembers).sort(
    (a, b) => rolePriority.indexOf(a) - rolePriority.indexOf(b)
  );
  
  // Count online members
  const onlineCount = mockMembers.filter(member => member.status !== 'offline').length;

  return (
    <div className="w-60 bg-discord-light-bg hidden md:block">
      <div className="p-4">
        <h3 className="text-xs font-semibold text-discord-muted uppercase mb-2">
          Members — {onlineCount} Online
        </h3>
        
        <div className="space-y-6 overflow-y-auto max-h-[calc(100vh-120px)]">
          {sortedRoles.map(role => (
            <div key={role}>
              <h4 className="text-xs font-semibold text-discord-muted uppercase mb-2">
                {role} — {groupedMembers[role].length}
              </h4>
              
              <div className="space-y-1">
                {groupedMembers[role].map(member => (
                  <div 
                    key={member.id} 
                    className="flex items-center p-1 rounded hover:bg-discord-bg cursor-pointer group"
                  >
                    <div className="relative mr-3">
                      <div className="w-8 h-8 rounded-full bg-discord-dark-bg flex items-center justify-center">
                        {member.avatar}
                      </div>
                      <div className={`absolute bottom-0 right-0 w-3 h-3 rounded-full border-2 border-discord-light-bg ${
                        member.status === 'online' ? 'bg-discord-green' :
                        member.status === 'idle' ? 'bg-discord-yellow' :
                        member.status === 'dnd' ? 'bg-discord-red' : 'bg-discord-muted'
                      }`}></div>
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="text-sm font-medium text-white truncate">
                        {member.username}
                      </div>
                      {member.activity && (
                        <div className="text-xs text-discord-muted truncate">
                          {member.activity}
                        </div>
                      )}
                    </div>
                    
                    <div className="hidden group-hover:block">
                      <button className="text-discord-muted hover:text-discord-text p-1">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-4 h-4">
                          <path fillRule="evenodd" d="M4.5 12a1.5 1.5 0 113 0 1.5 1.5 0 01-3 0zm6 0a1.5 1.5 0 113 0 1.5 1.5 0 01-3 0zm6 0a1.5 1.5 0 113 0 1.5 1.5 0 01-3 0z" clipRule="evenodd" />
                        </svg>
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default MembersList;
