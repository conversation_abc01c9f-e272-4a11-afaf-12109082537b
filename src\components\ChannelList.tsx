import React, { useState, useEffect } from 'react';
import { Link, useParams } from 'react-router-dom';
import { getServerChannels, createChannel } from '../services/channelService';
import { getServer } from '../services/serverService';
import { Channel, Server } from '../types';
import Tooltip from './ui/Tooltip';
import ChannelIcon from './ui/ChannelIcon';
import UserPanel from './UserPanel';
import ServerHeader from './ui/ServerHeader';
import CreateChannelModal from './modals/CreateChannelModal';

const ChannelList: React.FC = () => {
  const [channels, setChannels] = useState<Channel[]>([]);
  const [server, setServer] = useState<Server | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [isCreateChannelModalOpen, setIsCreateChannelModalOpen] = useState(false);
  const [collapsedCategories, setCollapsedCategories] = useState<Set<string>>(new Set());
  const { serverId, channelId } = useParams<{ serverId: string; channelId: string }>();

  useEffect(() => {
    const fetchServerAndChannels = async () => {
      if (!serverId) return;

      try {
        setLoading(true);

        // Fetch server details
        const serverData = await getServer(serverId);
        if (serverData) {
          setServer(serverData);
        }

        // Fetch channels
        const channelsData = await getServerChannels(serverId);
        setChannels(channelsData);
      } catch (err) {
        console.error('Error fetching server data:', err);
        setError('Failed to load server data');
      } finally {
        setLoading(false);
      }
    };

    fetchServerAndChannels();
  }, [serverId]);

  const handleChannelCreated = (newChannel: Channel) => {
    setChannels(prev => [...prev, newChannel]);
  };

  const toggleCategory = (categoryId: string) => {
    setCollapsedCategories(prev => {
      const newSet = new Set(prev);
      if (newSet.has(categoryId)) {
        newSet.delete(categoryId);
      } else {
        newSet.add(categoryId);
      }
      return newSet;
    });
  };

  // Group channels by type
  const textChannels = channels.filter(channel => channel.type === 'text');
  const voiceChannels = channels.filter(channel => channel.type === 'voice');
  const announcementChannels = channels.filter(channel => channel.type === 'announcement');

  return (
    <div className="w-60 bg-discord-darker flex flex-col">
      {/* Server header */}
      <ServerHeader
        server={server}
        onCreateChannel={() => setIsCreateChannelModalOpen(true)}
        onInvitePeople={() => {
          // TODO: Implement invite people
          console.log('Invite people clicked');
        }}
        onServerSettings={() => {
          // TODO: Implement server settings
          console.log('Server settings clicked');
        }}
        onLeaveServer={() => {
          // TODO: Implement leave server
          console.log('Leave server clicked');
        }}
      />

      {/* Channel list */}
      <div className="flex-1 overflow-y-auto">
        {error && (
          <div className="p-3 m-2 text-white bg-discord-red rounded-md text-sm">
            {error}
          </div>
        )}

        {loading ? (
          <div className="flex items-center justify-center h-20">
            <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-discord-primary"></div>
          </div>
        ) : (
          <div className="p-2">
            {/* Text channels */}
            <div className="mb-4">
              <div className="flex items-center justify-between px-1 mb-1 group">
                <button
                  onClick={() => toggleCategory('text-channels')}
                  className="flex items-center text-xs font-semibold text-discord-channel uppercase tracking-wider hover:text-discord-interactive-hover"
                >
                  <svg
                    width="12"
                    height="12"
                    viewBox="0 0 24 24"
                    className={`mr-1 transition-transform ${
                      collapsedCategories.has('text-channels') ? '-rotate-90' : ''
                    }`}
                  >
                    <path
                      fill="currentColor"
                      d="M16.59 8.59L12 13.17L7.41 8.59L6 10L12 16L18 10L16.59 8.59Z"
                    />
                  </svg>
                  Text Channels
                </button>
                <Tooltip content="Create Channel">
                  <button
                    onClick={() => setIsCreateChannelModalOpen(true)}
                    className="opacity-0 group-hover:opacity-100 text-discord-interactive-normal hover:text-discord-interactive-hover transition-opacity"
                  >
                    <svg width="18" height="18" viewBox="0 0 24 24">
                      <path
                        fill="currentColor"
                        d="M12 2C13.1046 2 14 2.89543 14 4V10H20C21.1046 10 22 10.8954 22 12C22 13.1046 21.1046 14 20 14H14V20C14 21.1046 13.1046 22 12 22C10.8954 22 10 21.1046 10 20V14H4C2.89543 14 2 13.1046 2 12C2 10.8954 2.89543 10 4 10H10V4C10 2.89543 10.8954 2 12 2Z"
                      />
                    </svg>
                  </button>
                </Tooltip>
              </div>

              {!collapsedCategories.has('text-channels') && (
                <div className="space-y-0.5">
                  {textChannels.length === 0 ? (
                    <p className="text-discord-interactive-muted text-sm px-2 py-1">No text channels</p>
                  ) : (
                    textChannels.map(channel => (
                      <Link
                        key={channel.id}
                        to={`/servers/${serverId}/channels/${channel.id}`}
                        className={`flex items-center px-2 py-1 mx-1 rounded group hover:bg-discord-hover-bg transition-colors ${
                          channelId === channel.id
                            ? 'bg-discord-active-bg text-white'
                            : 'text-discord-channel hover:text-discord-interactive-hover'
                        }`}
                      >
                        <ChannelIcon type="text" className="mr-1.5 text-discord-interactive-muted" />
                        <span className="truncate text-sm font-medium">{channel.name}</span>
                        {/* TODO: Add unread indicator and mention badge */}
                      </Link>
                    ))
                  )}
                </div>
              )}
            </div>

            {/* Voice channels */}
            {voiceChannels.length > 0 && (
              <div className="mb-4">
                <div className="flex items-center justify-between px-1 mb-1 group">
                  <button
                    onClick={() => toggleCategory('voice-channels')}
                    className="flex items-center text-xs font-semibold text-discord-channel uppercase tracking-wider hover:text-discord-interactive-hover"
                  >
                    <svg
                      width="12"
                      height="12"
                      viewBox="0 0 24 24"
                      className={`mr-1 transition-transform ${
                        collapsedCategories.has('voice-channels') ? '-rotate-90' : ''
                      }`}
                    >
                      <path
                        fill="currentColor"
                        d="M16.59 8.59L12 13.17L7.41 8.59L6 10L12 16L18 10L16.59 8.59Z"
                      />
                    </svg>
                    Voice Channels
                  </button>
                </div>

                {!collapsedCategories.has('voice-channels') && (
                  <div className="space-y-0.5">
                    {voiceChannels.map(channel => (
                      <div
                        key={channel.id}
                        className="flex items-center px-2 py-1 mx-1 rounded text-discord-channel hover:text-discord-interactive-hover hover:bg-discord-hover-bg cursor-pointer transition-colors"
                      >
                        <ChannelIcon type="voice" className="mr-1.5 text-discord-interactive-muted" />
                        <span className="truncate text-sm font-medium">{channel.name}</span>
                        {/* TODO: Add voice channel user list */}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            {/* Announcement channels */}
            {announcementChannels.length > 0 && (
              <div className="mb-4">
                <div className="flex items-center justify-between px-1 mb-1 group">
                  <button
                    onClick={() => toggleCategory('announcement-channels')}
                    className="flex items-center text-xs font-semibold text-discord-channel uppercase tracking-wider hover:text-discord-interactive-hover"
                  >
                    <svg
                      width="12"
                      height="12"
                      viewBox="0 0 24 24"
                      className={`mr-1 transition-transform ${
                        collapsedCategories.has('announcement-channels') ? '-rotate-90' : ''
                      }`}
                    >
                      <path
                        fill="currentColor"
                        d="M16.59 8.59L12 13.17L7.41 8.59L6 10L12 16L18 10L16.59 8.59Z"
                      />
                    </svg>
                    Announcements
                  </button>
                </div>

                {!collapsedCategories.has('announcement-channels') && (
                  <div className="space-y-0.5">
                    {announcementChannels.map(channel => (
                      <Link
                        key={channel.id}
                        to={`/servers/${serverId}/channels/${channel.id}`}
                        className={`flex items-center px-2 py-1 mx-1 rounded group hover:bg-discord-hover-bg transition-colors ${
                          channelId === channel.id
                            ? 'bg-discord-active-bg text-white'
                            : 'text-discord-channel hover:text-discord-interactive-hover'
                        }`}
                      >
                        <ChannelIcon type="announcement" className="mr-1.5 text-discord-interactive-muted" />
                        <span className="truncate text-sm font-medium">{channel.name}</span>
                      </Link>
                    ))}
                  </div>
                )}
              </div>
            )}
          </div>
        )}
      </div>

      {/* User panel */}
      <UserPanel />

      {/* Create channel modal */}
      <CreateChannelModal
        isOpen={isCreateChannelModalOpen}
        onClose={() => setIsCreateChannelModalOpen(false)}
        onChannelCreated={handleChannelCreated}
        serverId={serverId || ''}
      />
    </div>
  );
};

export default ChannelList;
