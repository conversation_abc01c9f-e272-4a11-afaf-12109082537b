# Supabase Setup Guide for Discord Clone

This guide will walk you through setting up Supabase for your Discord clone project.

## 1. Create a Supabase Account and Project

1. Go to [https://supabase.com](https://supabase.com) and sign up or log in
2. Click "New Project" to create a new project
3. Fill in the project details:
   - Name: `Discord Clone` (or any name you prefer)
   - Database Password: Create a secure password (save this somewhere safe)
   - Region: Choose the region closest to you or your users
4. Click "Create new project" and wait for it to be created (this may take a few minutes)

## 2. Get Your Supabase Credentials

1. Once your project is created, go to the project dashboard
2. Click on "Settings" in the sidebar
3. <PERSON>lick on "API" in the submenu
4. You'll find your "Project URL" and "anon/public" key there
5. Update your `.env.local` file with these values:
   ```
   REACT_APP_SUPABASE_URL=your_project_url
   REACT_APP_SUPABASE_ANON_KEY=your_anon_key
   ```

## 3. Configure Authentication Settings

1. In your Supabase dashboard, go to "Authentication" in the sidebar
2. <PERSON>lick on "Settings" in the submenu
3. Under "Site URL", enter your application URL (for local development, use `http://localhost:3000`)
4. Under "Redirect URLs", add `http://localhost:3000` for local development
5. Enable "Confirm email" if you want users to confirm their email addresses
6. Save your changes

## 4. Set Up Database Tables and Policies

You can set up the database tables and policies in two ways:

### Option 1: Using the SQL Editor

1. In your Supabase dashboard, go to "SQL Editor" in the sidebar
2. Click "New Query"
3. Copy and paste the SQL from the `supabase_setup.sql` file in this project
4. Click "Run" to execute the SQL

### Option 2: Using the Table Editor

1. In your Supabase dashboard, go to "Table Editor" in the sidebar
2. Click "New Table"
3. Create a table named `profiles` with the following columns:
   - `id` (type: uuid, primary key, references auth.users.id)
   - `username` (type: text, not null)
   - `discriminator` (type: text, not null)
   - `avatar` (type: text)
   - `status` (type: text, default: 'offline')
   - `custom_status` (type: text)
   - `created_at` (type: timestamp with time zone, default: now())
   - `updated_at` (type: timestamp with time zone, default: now())
4. Enable Row Level Security (RLS)
5. Add the following RLS policies:
   - For SELECT: Allow anyone to read profiles
   - For INSERT: Allow authenticated users to insert their own profile
   - For UPDATE: Allow authenticated users to update their own profile

## 5. Create a Trigger for New Users

To automatically create a profile when a new user signs up, you'll need to create a database function and trigger:

1. In your Supabase dashboard, go to "SQL Editor" in the sidebar
2. Click "New Query"
3. Copy and paste the following SQL:

```sql
-- Create a function to handle new user signups
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, username, discriminator, avatar)
  VALUES (
    NEW.id,
    COALESCE(NEW.raw_user_meta_data->>'username', split_part(NEW.email, '@', 1)),
    COALESCE(NEW.raw_user_meta_data->>'discriminator', '0000'),
    COALESCE(NEW.raw_user_meta_data->>'avatar', upper(left(COALESCE(NEW.raw_user_meta_data->>'username', split_part(NEW.email, '@', 1)), 1)))
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a trigger to automatically create a profile for new users
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();
```

4. Click "Run" to execute the SQL

## 6. Test Your Setup

1. Run your Discord clone application
2. Try to register a new user
3. Confirm that the user is created in Supabase
4. Check that a profile is automatically created for the user
5. Test logging in and out
6. Test updating the user profile

## Troubleshooting

If you encounter any issues:

1. Check the browser console for errors
2. Verify your Supabase URL and anon key in `.env.local`
3. Make sure your RLS policies are set up correctly
4. Check the Supabase logs in the dashboard under "Database" > "Logs"

## Next Steps

Once your authentication is working, you can extend your Discord clone by:

1. Adding real-time messaging using Supabase Realtime
2. Implementing server and channel creation
3. Adding friend requests and direct messages
4. Implementing voice chat
5. Adding file uploads and attachments
