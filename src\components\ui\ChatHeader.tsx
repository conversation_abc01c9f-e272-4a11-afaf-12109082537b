import React, { useState } from 'react';
import { Channel } from '../../types';
import Tooltip from './Tooltip';
import ChannelIcon from './ChannelIcon';

interface ChatHeaderProps {
  channel: Channel | null;
  onToggleMemberList?: () => void;
  onStartThread?: () => void;
  onToggleNotifications?: () => void;
  onShowPinnedMessages?: () => void;
  onSearch?: () => void;
  onShowInbox?: () => void;
  onShowHelp?: () => void;
  memberListVisible?: boolean;
}

const ChatHeader: React.FC<ChatHeaderProps> = ({
  channel,
  onToggleMemberList,
  onStartThread,
  onToggleNotifications,
  onShowPinnedMessages,
  onSearch,
  onShowInbox,
  onShowHelp,
  memberListVisible = true
}) => {
  const [isNotificationMuted, setIsNotificationMuted] = useState(false);

  const handleNotificationToggle = () => {
    setIsNotificationMuted(!isNotificationMuted);
    onToggleNotifications?.();
  };

  return (
    <div className="h-12 px-4 flex items-center justify-between border-b border-discord-separator bg-discord-bg shadow-sm">
      {/* Left side - Channel info */}
      <div className="flex items-center flex-1 min-w-0">
        <div className="flex items-center mr-3">
          <ChannelIcon 
            type={channel?.type || 'text'} 
            className="text-discord-interactive-normal mr-2" 
          />
          <h1 className="text-white font-semibold text-base">
            {channel?.name || 'Loading...'}
          </h1>
        </div>
        
        {channel?.topic && (
          <>
            <div className="w-px h-6 bg-discord-separator mx-2" />
            <p className="text-discord-channel text-sm truncate">
              {channel.topic}
            </p>
          </>
        )}
      </div>

      {/* Right side - Action buttons */}
      <div className="flex items-center space-x-2">
        {/* Start Thread */}
        <Tooltip content="Start Thread">
          <button
            onClick={onStartThread}
            className="w-6 h-6 flex items-center justify-center text-discord-interactive-normal hover:text-discord-interactive-hover transition-colors"
          >
            <svg width="20" height="20" viewBox="0 0 24 24">
              <path
                fill="currentColor"
                d="M5.43309 21C5.35842 21 5.30189 20.9326 5.31494 20.859L5.99991 17H2.14274C2.06819 17 2.01168 16.9327 2.02472 16.8591L2.33991 15.8591C2.34636 15.8267 2.36934 15.8004 2.40039 15.7894L6.99991 14.0894V10.8L2.40039 9.18966C2.36934 9.17905 2.34636 9.15278 2.33991 9.12035L2.02472 8.12035C2.01168 8.04677 2.06819 7.97941 2.14274 7.97941H5.99991L5.31494 4.14097C5.30189 4.06739 5.35842 4 5.43309 4H6.56691C6.64158 4 6.69811 4.06739 6.68506 4.14097L5.99991 8H9.99991L10.6851 4.14097C10.6981 4.06739 10.7547 4 10.8293 4H11.9631C12.0378 4 12.0943 4.06739 12.0813 4.14097L11.3961 8H15.8571C15.9317 8 15.9882 8.06736 15.9752 8.14094L15.66 9.14094C15.6535 9.17337 15.6306 9.19964 15.5995 9.21025L10.9999 10.8105V14.0894L15.5995 15.7894C15.6306 15.8 15.6535 15.8267 15.66 15.8591L15.9752 16.8591C15.9882 16.9326 15.9317 17 15.8571 17H11.3961L12.0813 20.859C12.0943 20.9326 12.0378 21 11.9631 21H10.8293C10.7547 21 10.6981 20.9326 10.6851 20.859L9.99991 17H5.99991L6.68506 20.859C6.69811 20.9326 6.64158 21 6.56691 21H5.43309ZM8.99991 8H7.99991L8.39991 10.3L8.99991 10.1V8ZM8.99991 14.9L8.39991 14.7L7.99991 17H8.99991V14.9Z"
              />
            </svg>
          </button>
        </Tooltip>

        {/* Notification Bell */}
        <Tooltip content={isNotificationMuted ? "Unmute Channel" : "Mute Channel"}>
          <button
            onClick={handleNotificationToggle}
            className={`w-6 h-6 flex items-center justify-center transition-colors ${
              isNotificationMuted 
                ? 'text-discord-red hover:text-discord-red' 
                : 'text-discord-interactive-normal hover:text-discord-interactive-hover'
            }`}
          >
            {isNotificationMuted ? (
              <svg width="20" height="20" viewBox="0 0 24 24">
                <path
                  fill="currentColor"
                  d="M18 10.7101C18 7.29508 15.3137 4.52251 12 4.52251C8.68629 4.52251 6 7.29508 6 10.7101V16.4834L4.10557 18.3778C3.428 19.0554 3.91421 20.1925 4.85279 20.1925H19.1472C20.0858 20.1925 20.572 19.0554 19.8944 18.3778L18 16.4834V10.7101ZM8.55493 21.5C9.24793 22.2 10.2 22.5 11.2 22.5H12.8C13.8 22.5 14.7521 22.2 15.4451 21.5"
                />
                <path
                  fill="currentColor"
                  d="M2.20711 1.79289C1.81658 1.40237 1.18342 1.40237 0.792893 1.79289C0.402369 2.18342 0.402369 2.81658 0.792893 3.20711L21.2071 23.6213C21.5976 24.0118 22.2308 24.0118 22.6213 23.6213C23.0118 23.2308 23.0118 22.5976 22.6213 22.2071L2.20711 1.79289Z"
                />
              </svg>
            ) : (
              <svg width="20" height="20" viewBox="0 0 24 24">
                <path
                  fill="currentColor"
                  d="M18 10.7101C18 7.29508 15.3137 4.52251 12 4.52251C8.68629 4.52251 6 7.29508 6 10.7101V16.4834L4.10557 18.3778C3.428 19.0554 3.91421 20.1925 4.85279 20.1925H19.1472C20.0858 20.1925 20.572 19.0554 19.8944 18.3778L18 16.4834V10.7101ZM8.55493 21.5C9.24793 22.2 10.2 22.5 11.2 22.5H12.8C13.8 22.5 14.7521 22.2 15.4451 21.5"
                />
              </svg>
            )}
          </button>
        </Tooltip>

        {/* Pinned Messages */}
        <Tooltip content="Pinned Messages">
          <button
            onClick={onShowPinnedMessages}
            className="w-6 h-6 flex items-center justify-center text-discord-interactive-normal hover:text-discord-interactive-hover transition-colors"
          >
            <svg width="20" height="20" viewBox="0 0 24 24">
              <path
                fill="currentColor"
                d="M22 12L13.44 2.24C13.11 1.86 12.61 1.64 12.09 1.64H4C2.9 1.64 2 2.54 2 3.64V20.36C2 21.46 2.9 22.36 4 22.36H20C21.1 22.36 22 21.46 22 20.36V12ZM15 9H9V7H15V9ZM17 13H7V11H17V13ZM17 17H7V15H17V17Z"
              />
            </svg>
          </button>
        </Tooltip>

        {/* Member List Toggle */}
        <Tooltip content="Member List">
          <button
            onClick={onToggleMemberList}
            className={`w-6 h-6 flex items-center justify-center transition-colors ${
              memberListVisible 
                ? 'text-white bg-discord-active-bg' 
                : 'text-discord-interactive-normal hover:text-discord-interactive-hover'
            }`}
          >
            <svg width="20" height="20" viewBox="0 0 24 24">
              <path
                fill="currentColor"
                d="M14 8.00598C14 10.211 12.206 12.006 10 12.006C7.795 12.006 6 10.211 6 8.00598C6 5.80098 7.794 4.00598 10 4.00598C12.206 4.00598 14 5.80098 14 8.00598ZM2 19.006C2 15.473 5.29 13.006 10 13.006C14.711 13.006 18 15.473 18 19.006V20.006H2V19.006Z"
              />
              <path
                fill="currentColor"
                d="M14 8.00598C14 10.211 12.206 12.006 10 12.006C7.795 12.006 6 10.211 6 8.00598C6 5.80098 7.794 4.00598 10 4.00598C12.206 4.00598 14 5.80098 14 8.00598ZM2 19.006C2 15.473 5.29 13.006 10 13.006C14.711 13.006 18 15.473 18 19.006V20.006H2V19.006Z"
              />
              <path
                fill="currentColor"
                d="M20.0001 20.006H22.0001V19.006C22.0001 16.4433 20.2697 14.4415 17.5213 13.5352C19.0621 14.9127 20.0001 16.8059 20.0001 19.006V20.006Z"
              />
              <path
                fill="currentColor"
                d="M14.8834 11.9077C16.6657 11.5044 18.0001 9.9077 18.0001 8.00598C18.0001 5.80098 16.206 4.00598 14.0001 4.00598C13.4954 4.00598 13.0172 4.11598 12.5858 4.31398C13.4374 5.05598 14.0001 6.44598 14.0001 8.00598C14.0001 9.56598 13.4374 10.956 12.5858 11.698C13.0172 11.896 13.4954 12.006 14.0001 12.006C14.3097 12.006 14.6056 11.9677 14.8834 11.9077Z"
              />
            </svg>
          </button>
        </Tooltip>

        {/* Search */}
        <Tooltip content="Search">
          <button
            onClick={onSearch}
            className="w-6 h-6 flex items-center justify-center text-discord-interactive-normal hover:text-discord-interactive-hover transition-colors"
          >
            <svg width="20" height="20" viewBox="0 0 24 24">
              <path
                fill="currentColor"
                d="M21.707 20.293L16.314 14.9C17.403 13.504 18 11.799 18 10C18 7.863 17.167 5.854 15.656 4.344C14.146 2.832 12.137 2 10 2C7.863 2 5.854 2.832 4.344 4.344C2.833 5.854 2 7.863 2 10C2 12.137 2.833 14.146 4.344 15.656C5.854 17.168 7.863 18 10 18C11.799 18 13.504 17.404 14.9 16.314L20.293 21.706L21.707 20.293ZM10 16C8.397 16 6.891 15.376 5.758 14.243C4.624 13.11 4 11.603 4 10C4 8.398 4.624 6.891 5.758 5.758C6.891 4.624 8.397 4 10 4C11.603 4 13.109 4.624 14.242 5.758C15.376 6.891 16 8.398 16 10C16 11.603 15.376 13.11 14.242 14.243C13.109 15.376 11.603 16 10 16Z"
              />
            </svg>
          </button>
        </Tooltip>

        {/* Inbox */}
        <Tooltip content="Inbox">
          <button
            onClick={onShowInbox}
            className="w-6 h-6 flex items-center justify-center text-discord-interactive-normal hover:text-discord-interactive-hover transition-colors"
          >
            <svg width="20" height="20" viewBox="0 0 24 24">
              <path
                fill="currentColor"
                d="M19 3H4.99C3.88 3 3.01 3.89 3.01 5L3 19C3 20.1 3.88 21 4.99 21H19C20.1 21 21 20.1 21 19V5C21 3.89 20.1 3 19 3ZM19 19H5V8L12 13L19 8V19ZM12 11L5 6H19L12 11Z"
              />
            </svg>
          </button>
        </Tooltip>

        {/* Help */}
        <Tooltip content="Help">
          <button
            onClick={onShowHelp}
            className="w-6 h-6 flex items-center justify-center text-discord-interactive-normal hover:text-discord-interactive-hover transition-colors"
          >
            <svg width="20" height="20" viewBox="0 0 24 24">
              <path
                fill="currentColor"
                d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM13 19H11V17H13V19ZM15.07 11.25L14.17 12.17C13.45 12.9 13 13.5 13 15H11V14.5C11 13.4 11.45 12.4 12.17 11.67L13.41 10.41C13.78 10.05 14 9.55 14 9C14 7.9 13.1 7 12 7C10.9 7 10 7.9 10 9H8C8 6.79 9.79 5 12 5C14.21 5 16 6.79 16 9C16 9.88 15.64 10.68 15.07 11.25Z"
              />
            </svg>
          </button>
        </Tooltip>
      </div>
    </div>
  );
};

export default ChatHeader;
