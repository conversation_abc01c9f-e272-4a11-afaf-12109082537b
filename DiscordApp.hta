<!DOCTYPE html>
<html>
<head>
  <title>Discord</title>
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <hta:application
    id="DiscordClone"
    applicationname="Discord"
    border="thin"
    borderstyle="normal"
    caption="yes"
    icon="discord_icon.ico"
    maximizebutton="yes"
    minimizebutton="yes"
    showintaskbar="yes"
    singleinstance="yes"
    sysmenu="yes"
    version="1.0"
    windowstate="normal"
  />
  <style>
    body {
      font-family: 'gg sans', 'Noto Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
      margin: 0;
      padding: 0;
      background-color: #313338;
      color: #dbdee1;
      height: 100vh;
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }

    .app {
      display: flex;
      height: 100vh;
    }

    .sidebar {
      width: 72px;
      background-color: #1e1f22;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 12px 0;
    }

    .server-icon {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      background-color: #5865f2;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 8px;
      font-size: 20px;
      cursor: pointer;
      transition: border-radius 0.2s;
    }

    .server-icon:hover {
      border-radius: 16px;
    }

    .channel-sidebar {
      width: 240px;
      background-color: #2b2d31;
      display: flex;
      flex-direction: column;
    }

    .server-header {
      padding: 16px;
      border-bottom: 1px solid #1e1f22;
      font-weight: bold;
      color: #f2f3f5;
    }

    .channels-container {
      padding: 8px;
      flex: 1;
      overflow-y: auto;
    }

    .channel-category {
      margin-bottom: 8px;
    }

    .category-header {
      text-transform: uppercase;
      font-size: 12px;
      font-weight: 600;
      color: #949ba4;
      margin-bottom: 4px;
      padding: 0 8px;
    }

    .channel {
      display: flex;
      align-items: center;
      padding: 6px 8px;
      border-radius: 4px;
      color: #949ba4;
      cursor: pointer;
    }

    .channel:hover {
      background-color: #35373c;
      color: #dbdee1;
    }

    .channel-icon {
      margin-right: 6px;
      font-size: 20px;
    }

    .chat-area {
      flex: 1;
      display: flex;
      flex-direction: column;
    }

    .chat-header {
      padding: 12px 16px;
      border-bottom: 1px solid #1e1f22;
      display: flex;
      align-items: center;
      background-color: #313338;
    }

    .chat-messages {
      flex: 1;
      padding: 16px;
      overflow-y: auto;
      background-color: #313338;
    }

    .message {
      display: flex;
      margin-bottom: 16px;
    }

    .message-avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: #5865f2;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16px;
    }

    .message-content {
      flex: 1;
    }

    .message-header {
      display: flex;
      align-items: baseline;
      margin-bottom: 4px;
    }

    .message-author {
      font-weight: 500;
      margin-right: 8px;
    }

    .message-time {
      font-size: 12px;
      color: #8e9297;
    }

    .message-text {
      color: #dcddde;
    }

    .message-input {
      padding: 16px;
      margin: 0 16px 16px;
      background-color: #383a40;
      border-radius: 8px;
      display: flex;
      align-items: center;
    }

    .message-input input {
      flex: 1;
      background: transparent;
      border: none;
      color: #dbdee1;
      font-size: 16px;
      outline: none;
    }

    .message-input input::placeholder {
      color: #87898c;
    }

    .members-list {
      width: 240px;
      background-color: #2b2d31;
      padding: 16px;
      overflow-y: auto;
    }

    .members-header {
      text-transform: uppercase;
      font-size: 12px;
      font-weight: 600;
      color: #949ba4;
      margin-bottom: 8px;
    }

    .member {
      display: flex;
      align-items: center;
      padding: 6px 0;
      border-radius: 4px;
      cursor: pointer;
    }

    .member:hover {
      background-color: #35373c;
    }

    .member-avatar {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background-color: #5865f2;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 8px;
      position: relative;
    }

    .status-indicator {
      width: 10px;
      height: 10px;
      border-radius: 50%;
      background-color: #23a559;
      border: 2px solid #2b2d31;
      position: absolute;
      bottom: -2px;
      right: -2px;
    }

    .member-name {
      font-size: 14px;
      color: #949ba4;
    }
  </style>
  <script language="VBScript">
    Sub Window_OnLoad
      window.resizeTo 1400, 900
      window.moveTo (screen.availWidth - 1400) / 2, (screen.availHeight - 900) / 2
    End Sub

    Sub CreateDesktopShortcut
      Set objWShell = CreateObject("WScript.Shell")
      strDesktop = objWShell.SpecialFolders("Desktop")

      Set objShortcut = objWShell.CreateShortcut(strDesktop & "\Discord.lnk")
      objShortcut.TargetPath = window.location.pathname
      objShortcut.WorkingDirectory = Left(window.location.pathname, InStrRev(window.location.pathname, "\") - 1)
      objShortcut.Description = "Discord Clone Application"
      objShortcut.IconLocation = objShortcut.WorkingDirectory & "\discord_icon.ico"
      objShortcut.Save

      MsgBox "Desktop shortcut created successfully!", vbInformation, "Discord"
    End Sub

    Sub CreateStartMenuShortcut
      Set objWShell = CreateObject("WScript.Shell")
      strStartMenu = objWShell.SpecialFolders("StartMenu") & "\Programs"

      Set objShortcut = objWShell.CreateShortcut(strStartMenu & "\Discord.lnk")
      objShortcut.TargetPath = window.location.pathname
      objShortcut.WorkingDirectory = Left(window.location.pathname, InStrRev(window.location.pathname, "\") - 1)
      objShortcut.Description = "Discord Clone Application"
      objShortcut.IconLocation = objShortcut.WorkingDirectory & "\discord_icon.ico"
      objShortcut.Save

      MsgBox "Start Menu shortcut created successfully!", vbInformation, "Discord"
    End Sub
  </script>
</head>
<body>
  <div class="app">
    <!-- Server Sidebar -->
    <div class="sidebar">
      <div class="server-icon" style="background-color: #5865f2;" title="Discord Clone">D</div>
      <!-- Add server button -->
      <div class="server-icon" style="background-color: #36393f; border: 2px dashed #4f545c; color: #3ba55d;" title="Add a Server">+</div>
    </div>

    <!-- Channel Sidebar -->
    <div class="channel-sidebar">
      <div class="server-header">Discord Clone</div>

      <!-- Friends Section -->
      <div onclick="openFriends()" style="padding: 8px 16px; margin: 4px 8px; border-radius: 4px; cursor: pointer; color: #949ba4; display: flex; align-items: center; transition: background-color 0.2s;" onmouseover="this.style.backgroundColor='#35373c'; this.style.color='#dbdee1'" onmouseout="this.style.backgroundColor='transparent'; this.style.color='#949ba4'">
        <span style="margin-right: 8px;">👥</span>
        Friends
      </div>

      <div class="channels-container">
        <div class="channel-category">
          <div class="category-header">Text Channels</div>
          <div class="channel">
            <span class="channel-icon">#</span>
            <span>general</span>
          </div>
          <div class="channel">
            <span class="channel-icon">#</span>
            <span>welcome</span>
          </div>
          <div class="channel">
            <span class="channel-icon">#</span>
            <span>rules</span>
          </div>
        </div>

        <div class="channel-category">
          <div class="category-header">Voice Channels</div>
          <div class="channel">
            <span class="channel-icon">🔊</span>
            <span>General Voice</span>
          </div>
          <div class="channel">
            <span class="channel-icon">🔊</span>
            <span>Gaming</span>
          </div>
          <div class="channel">
            <span class="channel-icon">🔊</span>
            <span>Music</span>
          </div>
        </div>
      </div>

      <!-- User Panel at bottom of channel sidebar -->
      <div class="user-panel" style="background-color: #232428; padding: 8px; display: flex; align-items: center; border-top: 1px solid #1e1f22; height: 52px; box-sizing: border-box;">
        <div class="user-info" style="display: flex; align-items: center; flex: 1; min-width: 0;">
          <div class="user-avatar" style="width: 32px; height: 32px; border-radius: 50%; background-color: #5865f2; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; margin-right: 8px; flex-shrink: 0;">
            Y
          </div>
          <div class="user-details" style="flex: 1; min-width: 0;">
            <div style="color: #f2f3f5; font-size: 14px; font-weight: 600; line-height: 18px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">You</div>
            <div style="color: #b5bac1; font-size: 12px; line-height: 16px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">#0001</div>
          </div>
        </div>
        <div class="user-controls" style="display: flex; gap: 0;">
          <button onclick="toggleMute()" title="Mute" style="background: none; border: none; color: #b5bac1; padding: 4px; border-radius: 4px; cursor: pointer; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; font-size: 18px; transition: background-color 0.2s, color 0.2s;" onmouseover="this.style.backgroundColor='#35373c'" onmouseout="this.style.backgroundColor='transparent'">
            🎤
          </button>
          <button onclick="toggleDeafen()" title="Deafen" style="background: none; border: none; color: #b5bac1; padding: 4px; border-radius: 4px; cursor: pointer; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; font-size: 18px; transition: background-color 0.2s, color 0.2s;" onmouseover="this.style.backgroundColor='#35373c'" onmouseout="this.style.backgroundColor='transparent'">
            🎧
          </button>
          <button onclick="openSettings()" title="User Settings" style="background: none; border: none; color: #b5bac1; padding: 4px; border-radius: 4px; cursor: pointer; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; font-size: 18px; transition: background-color 0.2s, color 0.2s;" onmouseover="this.style.backgroundColor='#35373c'" onmouseout="this.style.backgroundColor='transparent'">
            ⚙️
          </button>
        </div>
      </div>
    </div>

    <!-- Chat Area -->
    <div class="chat-area">
      <div class="chat-header" style="padding: 12px 16px; border-bottom: 1px solid #1e1f22; display: flex; align-items: center; height: 48px; box-sizing: border-box; background-color: #313338;">
        <span class="channel-icon" style="color: #80848e; margin-right: 8px; font-size: 20px;">#</span>
        <span style="color: #f2f3f5; font-weight: 600; font-size: 16px;">general</span>
        <div style="flex: 1;"></div>
        <div style="display: flex; align-items: center; gap: 16px;">
          <button title="Start Thread" style="background: none; border: none; color: #b5bac1; cursor: pointer; padding: 4px; border-radius: 4px; transition: background-color 0.2s;" onmouseover="this.style.backgroundColor='#35373c'" onmouseout="this.style.backgroundColor='transparent'">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor"><path d="M5.43309 21C5.35842 21 5.30189 20.9326 5.31494 20.859L5.99991 17H2.14274C2.06819 17 2.01168 16.9327 2.02472 16.8591L2.33079 15.8215C2.34383 15.7479 2.40036 15.6805 2.47491 15.6805H6.34154L7.40616 10.9348H3.54899C3.47444 10.9348 3.41791 10.8674 3.43095 10.7938L3.73702 9.75618C3.75006 9.68257 3.80659 9.61524 3.88114 9.61524H7.74777L8.43274 6H9.56726L8.88229 9.61524H13.2465L13.9315 6H15.0661L14.3811 9.61524H18.2382C18.3128 9.61524 18.3693 9.68257 18.3563 9.75618L18.0502 10.7938C18.0372 10.8674 17.9806 10.9348 17.9061 10.9348H14.0394L12.9748 15.6805H16.8319C16.9065 15.6805 16.963 15.7479 16.95 15.8215L16.6439 16.8591C16.6309 16.9327 16.5743 17 16.4998 17H12.6331L11.9481 20.859C11.9351 20.9326 11.8786 21 11.8041 21H10.6695C10.5949 21 10.5384 20.9326 10.5514 20.859L11.2364 17H6.87229L6.18732 20.859C6.17428 20.9326 6.11775 21 6.04320 21H5.43309ZM7.58847 15.6805H11.9526L13.0172 10.9348H8.65309L7.58847 15.6805Z"/></svg>
          </button>
          <button title="Notification Settings" style="background: none; border: none; color: #b5bac1; cursor: pointer; padding: 4px; border-radius: 4px; transition: background-color 0.2s;" onmouseover="this.style.backgroundColor='#35373c'" onmouseout="this.style.backgroundColor='transparent'">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor"><path d="M18 9V14C18 15.657 19.343 17 21 17V18H3V17C4.657 17 6 15.657 6 14V9C6 5.686 8.686 3 12 3C15.314 3 18 5.686 18 9ZM11.9999 21C10.5239 21 9.24793 20.19 8.55493 19H15.4449C14.7519 20.19 13.4759 21 11.9999 21Z"/></svg>
          </button>
          <button title="Pinned Messages" style="background: none; border: none; color: #b5bac1; cursor: pointer; padding: 4px; border-radius: 4px; transition: background-color 0.2s;" onmouseover="this.style.backgroundColor='#35373c'" onmouseout="this.style.backgroundColor='transparent'">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor"><path d="M22 12L13.44 2.241C13.1824 1.9607 12.8284 1.80334 12.4575 1.80334C12.0866 1.80334 11.7326 1.9607 11.475 2.241L2 12H9V21H15V12H22Z"/></svg>
          </button>
          <button title="Member List" style="background: none; border: none; color: #b5bac1; cursor: pointer; padding: 4px; border-radius: 4px; transition: background-color 0.2s;" onmouseover="this.style.backgroundColor='#35373c'" onmouseout="this.style.backgroundColor='transparent'">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor"><path d="M14 8.00598C14 10.211 12.206 12.006 10 12.006C7.795 12.006 6 10.211 6 8.00598C6 5.80098 7.794 4.00598 10 4.00598C12.206 4.00598 14 5.80098 14 8.00598ZM2 19.006C2 15.473 5.29 13.006 10 13.006C14.711 13.006 18 15.473 18 19.006V20.006H2V19.006Z"/><path d="M14 8.00598C14 10.211 12.206 12.006 10 12.006C7.795 12.006 6 10.211 6 8.00598C6 5.80098 7.794 4.00598 10 4.00598C12.206 4.00598 14 5.80098 14 8.00598ZM2 19.006C2 15.473 5.29 13.006 10 13.006C14.711 13.006 18 15.473 18 19.006V20.006H2V19.006Z"/><path d="M20.0001 20.006H22.0001V19.006C22.0001 16.4433 20.2697 14.4415 17.5213 13.5352C19.0621 14.9127 20.0001 16.8059 20.0001 19.006V20.006Z"/><path d="M14.8834 11.9077C16.6657 11.5044 18.0001 9.9077 18.0001 8.00598C18.0001 5.80098 16.206 4.00598 14.0001 4.00598C13.4393 4.00598 12.9169 4.15494 12.4597 4.41491C13.4403 5.22355 14.0001 6.49956 14.0001 8.00598C14.0001 9.51239 13.4403 10.7884 12.4597 11.597C12.9169 11.857 13.4393 12.006 14.0001 12.006C14.3051 12.006 14.6002 11.9682 14.8834 11.9077Z"/></svg>
          </button>
          <input type="text" placeholder="Search" style="background-color: #1e1f22; border: none; color: #dbdee1; padding: 4px 8px; border-radius: 4px; width: 144px; font-size: 14px;" />
          <button title="Inbox" style="background: none; border: none; color: #b5bac1; cursor: pointer; padding: 4px; border-radius: 4px; transition: background-color 0.2s;" onmouseover="this.style.backgroundColor='#35373c'" onmouseout="this.style.backgroundColor='transparent'">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor"><path d="M19 3H4.99C3.88 3 3.01 3.89 3.01 5L3 19C3 20.1 3.88 21 4.99 21H19C20.1 21 21 20.1 21 19V5C21 3.89 20.1 3 19 3ZM19 15H15C15 16.66 13.65 18 12 18C10.35 18 9 16.66 9 15H4.99V5H19V15Z"/></svg>
          </button>
          <button title="Help" style="background: none; border: none; color: #b5bac1; cursor: pointer; padding: 4px; border-radius: 4px; transition: background-color 0.2s;" onmouseover="this.style.backgroundColor='#35373c'" onmouseout="this.style.backgroundColor='transparent'">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor"><path d="M12 2C6.486 2 2 6.487 2 12C2 17.515 6.486 22 12 22C17.514 22 22 17.515 22 12C22 6.487 17.514 2 12 2ZM12 18.25C11.31 18.25 10.75 17.691 10.75 17C10.75 16.31 11.31 15.75 12 15.75C12.69 15.75 13.25 16.31 13.25 17C13.25 17.691 12.69 18.25 12 18.25ZM13 13.875V15H11V12H12C13.104 12 14 11.103 14 10C14 8.896 13.104 8 12 8C10.896 8 10 8.896 10 10H8C8 7.795 9.795 6 12 6C14.205 6 16 7.795 16 10C16 11.861 14.723 13.429 13 13.875Z"/></svg>
          </button>
        </div>
      </div>

      <div class="chat-messages">
        <!-- Welcome message -->
        <div class="welcome-message" style="text-align: center; padding: 40px 20px; color: #949ba4;">
          <div style="font-size: 48px; margin-bottom: 16px;">👋</div>
          <h2 style="color: #f2f3f5; margin-bottom: 8px;">Welcome to Discord Clone!</h2>
          <p>This is your personal Discord clone. Start by inviting friends or creating channels.</p>
        </div>
      </div>

      <div class="message-input">
        <input type="text" placeholder="Message #general">
      </div>


    </div>

    <!-- Members List -->
    <div class="members-list">
      <div class="members-header">Members — 1 Online</div>

      <div class="role-section">
        <div class="role-header" style="color: #949ba4; font-size: 12px; font-weight: 600; margin: 16px 8px 4px; text-transform: uppercase;">OWNER — 1</div>
        <div class="member">
          <div class="member-avatar">
            Y
            <div class="status-indicator" style="background-color: #23a559;"></div>
          </div>
          <div class="member-name" style="color: #f2f3f5;">You</div>
        </div>
      </div>

      <!-- Empty state for other members -->
      <div style="text-align: center; padding: 20px; color: #80848e; font-size: 14px;">
        <div style="margin-bottom: 8px;">🏠</div>
        <div>It's just you for now</div>
        <div style="font-size: 12px; margin-top: 4px;">Invite friends to get started!</div>
      </div>
    </div>
  </div>

  <!-- Settings Modal -->
  <div id="settingsModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.85); z-index: 1000;">
    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 80%; max-width: 800px; height: 80%; background-color: #36393f; border-radius: 8px; display: flex; overflow: hidden;">

      <!-- Settings Sidebar -->
      <div style="width: 200px; background-color: #2f3136; padding: 20px 0;">
        <div style="color: #8e9297; font-size: 12px; font-weight: 600; margin: 0 16px 8px; text-transform: uppercase;">User Settings</div>
        <div class="settings-item active" onclick="showSettingsTab('account')" style="padding: 8px 16px; color: #dcddde; cursor: pointer; margin: 2px 8px; border-radius: 4px; background-color: #5865f2;">My Account</div>
        <div class="settings-item" onclick="showSettingsTab('profile')" style="padding: 8px 16px; color: #dcddde; cursor: pointer; margin: 2px 8px; border-radius: 4px;">Profile</div>
        <div class="settings-item" onclick="showSettingsTab('appearance')" style="padding: 8px 16px; color: #dcddde; cursor: pointer; margin: 2px 8px; border-radius: 4px;">Appearance</div>
        <div class="settings-item" onclick="showSettingsTab('notifications')" style="padding: 8px 16px; color: #dcddde; cursor: pointer; margin: 2px 8px; border-radius: 4px;">Notifications</div>
        <div class="settings-item" onclick="showSettingsTab('privacy')" style="padding: 8px 16px; color: #dcddde; cursor: pointer; margin: 2px 8px; border-radius: 4px;">Privacy & Safety</div>

        <div style="color: #8e9297; font-size: 12px; font-weight: 600; margin: 16px 16px 8px; text-transform: uppercase;">App Settings</div>
        <div class="settings-item" onclick="showSettingsTab('voice')" style="padding: 8px 16px; color: #dcddde; cursor: pointer; margin: 2px 8px; border-radius: 4px;">Voice & Video</div>
        <div class="settings-item" onclick="showSettingsTab('keybinds')" style="padding: 8px 16px; color: #dcddde; cursor: pointer; margin: 2px 8px; border-radius: 4px;">Keybinds</div>
        <div class="settings-item" onclick="showSettingsTab('language')" style="padding: 8px 16px; color: #dcddde; cursor: pointer; margin: 2px 8px; border-radius: 4px;">Language</div>

        <div style="margin-top: 20px; padding: 8px 16px;">
          <div class="settings-item" onclick="logout()" style="padding: 8px 16px; color: #ed4245; cursor: pointer; margin: 2px 8px; border-radius: 4px;">Log Out</div>
        </div>
      </div>

      <!-- Settings Content -->
      <div style="flex: 1; padding: 20px; overflow-y: auto;">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
          <h2 id="settingsTitle" style="color: #ffffff; margin: 0;">My Account</h2>
          <button onclick="closeSettings()" style="background: none; border: none; color: #dcddde; font-size: 20px; cursor: pointer; padding: 4px;">✕</button>
        </div>

        <div id="settingsContent">
          <!-- Account Settings (Default) -->
          <div id="accountSettings">
            <div style="background-color: #2f3136; padding: 16px; border-radius: 8px; margin-bottom: 20px;">
              <div style="display: flex; align-items: center; margin-bottom: 16px;">
                <div style="width: 80px; height: 80px; border-radius: 50%; background-color: #5865f2; display: flex; align-items: center; justify-content: center; color: white; font-size: 32px; font-weight: bold; margin-right: 16px;">
                  Y
                </div>
                <div>
                  <div style="color: #ffffff; font-size: 20px; font-weight: 600;">You</div>
                  <div style="color: #b9bbbe; font-size: 16px;">#0001</div>
                </div>
              </div>
              <button style="background-color: #5865f2; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">Edit</button>
            </div>

            <div style="margin-bottom: 20px;">
              <label style="color: #b9bbbe; font-size: 12px; font-weight: 600; text-transform: uppercase; margin-bottom: 8px; display: block;">Username</label>
              <input type="text" value="You" style="background-color: #40444b; border: none; color: #dcddde; padding: 10px; border-radius: 4px; width: 100%; box-sizing: border-box;">
            </div>

            <div style="margin-bottom: 20px;">
              <label style="color: #b9bbbe; font-size: 12px; font-weight: 600; text-transform: uppercase; margin-bottom: 8px; display: block;">Email</label>
              <input type="email" value="<EMAIL>" style="background-color: #40444b; border: none; color: #dcddde; padding: 10px; border-radius: 4px; width: 100%; box-sizing: border-box;">
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Friends Modal -->
  <div id="friendsModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.85); z-index: 1000;">
    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 80%; max-width: 600px; height: 60%; background-color: #36393f; border-radius: 8px; padding: 20px;">
      <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
        <h2 style="color: #ffffff; margin: 0;">Friends</h2>
        <button onclick="closeFriends()" style="background: none; border: none; color: #dcddde; font-size: 20px; cursor: pointer; padding: 4px;">✕</button>
      </div>

      <div style="text-align: center; padding: 40px 20px; color: #8e9297;">
        <div style="font-size: 48px; margin-bottom: 16px;">😔</div>
        <h3 style="color: #ffffff; margin-bottom: 8px;">No friends yet</h3>
        <p>You don't have any friends added yet. Start by sending friend requests!</p>
        <button style="background-color: #5865f2; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin-top: 16px;">Add Friend</button>
      </div>
    </div>
  </div>

  <script>
    let isMuted = false;
    let isDeafened = false;

    function toggleMute() {
      isMuted = !isMuted;
      const button = event.target;
      button.innerHTML = isMuted ? '🔇' : '🎤';
      button.style.color = isMuted ? '#ed4245' : '#b9bbbe';
    }

    function toggleDeafen() {
      isDeafened = !isDeafened;
      const button = event.target;
      button.innerHTML = isDeafened ? '🔇' : '🎧';
      button.style.color = isDeafened ? '#ed4245' : '#b9bbbe';
    }

    function openSettings() {
      document.getElementById('settingsModal').style.display = 'block';
    }

    function closeSettings() {
      document.getElementById('settingsModal').style.display = 'none';
    }

    function openFriends() {
      document.getElementById('friendsModal').style.display = 'block';
    }

    function closeFriends() {
      document.getElementById('friendsModal').style.display = 'none';
    }

    function showSettingsTab(tab) {
      // Remove active class from all items
      const items = document.querySelectorAll('.settings-item');
      items.forEach(item => {
        item.style.backgroundColor = 'transparent';
      });

      // Add active class to clicked item
      event.target.style.backgroundColor = '#5865f2';

      // Update title
      const titles = {
        'account': 'My Account',
        'profile': 'Profile',
        'appearance': 'Appearance',
        'notifications': 'Notifications',
        'privacy': 'Privacy & Safety',
        'voice': 'Voice & Video',
        'keybinds': 'Keybinds',
        'language': 'Language'
      };

      document.getElementById('settingsTitle').textContent = titles[tab] || 'Settings';

      // Show different content based on tab
      const content = document.getElementById('settingsContent');
      if (tab === 'appearance') {
        content.innerHTML = '<div>' +
          '<h3 style="color: #ffffff; margin-bottom: 16px;">Theme</h3>' +
          '<div style="margin-bottom: 20px;">' +
            '<label style="color: #b9bbbe; font-size: 12px; font-weight: 600; text-transform: uppercase; margin-bottom: 8px; display: block;">Theme</label>' +
            '<select style="background-color: #40444b; border: none; color: #dcddde; padding: 10px; border-radius: 4px; width: 200px;">' +
              '<option>Dark</option>' +
              '<option>Light</option>' +
            '</select>' +
          '</div>' +
        '</div>';
      } else if (tab === 'notifications') {
        content.innerHTML = '<div>' +
          '<h3 style="color: #ffffff; margin-bottom: 16px;">Notifications</h3>' +
          '<div style="margin-bottom: 20px;">' +
            '<label style="display: flex; align-items: center; color: #dcddde; cursor: pointer;">' +
              '<input type="checkbox" checked style="margin-right: 8px;">' +
              'Enable desktop notifications' +
            '</label>' +
          '</div>' +
          '<div style="margin-bottom: 20px;">' +
            '<label style="display: flex; align-items: center; color: #dcddde; cursor: pointer;">' +
              '<input type="checkbox" style="margin-right: 8px;">' +
              'Play sound for notifications' +
            '</label>' +
          '</div>' +
        '</div>';
      } else {
        // Default to account settings
        content.innerHTML = document.getElementById('accountSettings').innerHTML;
      }
    }

    function logout() {
      if (confirm('Are you sure you want to log out?')) {
        alert('Logged out successfully!');
        closeSettings();
      }
    }

    // Close modals when clicking outside
    window.onclick = function(event) {
      const settingsModal = document.getElementById('settingsModal');
      const friendsModal = document.getElementById('friendsModal');

      if (event.target === settingsModal) {
        closeSettings();
      }
      if (event.target === friendsModal) {
        closeFriends();
      }
    }

    // Add click handler for Friends button
    document.addEventListener('DOMContentLoaded', function() {
      // Add Friends button functionality if it exists
      const friendsButton = document.querySelector('[onclick="openFriends()"]');
      if (!friendsButton) {
        // Create a friends button in the channel sidebar
        const channelSidebar = document.querySelector('.channel-sidebar');
        if (channelSidebar) {
          const friendsBtn = document.createElement('div');
          friendsBtn.innerHTML = '<div onclick="openFriends()" style="padding: 8px 16px; margin: 4px 8px; border-radius: 4px; cursor: pointer; color: #8e9297; display: flex; align-items: center;">' +
            '<span style="margin-right: 8px;">👥</span>' +
            'Friends' +
          '</div>';
          channelSidebar.insertBefore(friendsBtn, channelSidebar.firstChild.nextSibling);
        }
      }
    });
  </script>
</body>
</html>
