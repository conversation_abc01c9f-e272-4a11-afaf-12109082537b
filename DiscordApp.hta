<!DOCTYPE html>
<html>
<head>
  <title>Discord</title>
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <hta:application
    id="DiscordClone"
    applicationname="Discord"
    border="thin"
    borderstyle="normal"
    caption="yes"
    icon="discord_icon.ico"
    maximizebutton="yes"
    minimizebutton="yes"
    showintaskbar="yes"
    singleinstance="yes"
    sysmenu="yes"
    version="1.0"
    windowstate="normal"
  />
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      margin: 0;
      padding: 0;
      background-color: #36393f;
      color: #dcddde;
      height: 100vh;
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }

    .app {
      display: flex;
      height: 100vh;
    }

    .sidebar {
      width: 72px;
      background-color: #202225;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 12px 0;
    }

    .server-icon {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      background-color: #5865f2;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 8px;
      font-size: 20px;
      cursor: pointer;
      transition: border-radius 0.2s;
    }

    .server-icon:hover {
      border-radius: 16px;
    }

    .channel-sidebar {
      width: 240px;
      background-color: #2f3136;
      display: flex;
      flex-direction: column;
    }

    .server-header {
      padding: 16px;
      border-bottom: 1px solid #202225;
      font-weight: bold;
    }

    .channels-container {
      padding: 8px;
      flex: 1;
      overflow-y: auto;
    }

    .channel-category {
      margin-bottom: 8px;
    }

    .category-header {
      text-transform: uppercase;
      font-size: 12px;
      font-weight: 600;
      color: #8e9297;
      margin-bottom: 4px;
      padding: 0 8px;
    }

    .channel {
      display: flex;
      align-items: center;
      padding: 6px 8px;
      border-radius: 4px;
      color: #8e9297;
      cursor: pointer;
    }

    .channel:hover {
      background-color: #36393f;
      color: #dcddde;
    }

    .channel-icon {
      margin-right: 6px;
      font-size: 20px;
    }

    .chat-area {
      flex: 1;
      display: flex;
      flex-direction: column;
    }

    .chat-header {
      padding: 12px 16px;
      border-bottom: 1px solid #202225;
      display: flex;
      align-items: center;
    }

    .chat-messages {
      flex: 1;
      padding: 16px;
      overflow-y: auto;
    }

    .message {
      display: flex;
      margin-bottom: 16px;
    }

    .message-avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: #5865f2;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16px;
    }

    .message-content {
      flex: 1;
    }

    .message-header {
      display: flex;
      align-items: baseline;
      margin-bottom: 4px;
    }

    .message-author {
      font-weight: 500;
      margin-right: 8px;
    }

    .message-time {
      font-size: 12px;
      color: #8e9297;
    }

    .message-text {
      color: #dcddde;
    }

    .message-input {
      padding: 16px;
      margin: 0 16px 16px;
      background-color: #40444b;
      border-radius: 8px;
      display: flex;
      align-items: center;
    }

    .message-input input {
      flex: 1;
      background: transparent;
      border: none;
      color: #dcddde;
      font-size: 16px;
      outline: none;
    }

    .message-input input::placeholder {
      color: #8e9297;
    }

    .members-list {
      width: 240px;
      background-color: #2f3136;
      padding: 16px;
      overflow-y: auto;
    }

    .members-header {
      text-transform: uppercase;
      font-size: 12px;
      font-weight: 600;
      color: #8e9297;
      margin-bottom: 8px;
    }

    .member {
      display: flex;
      align-items: center;
      padding: 6px 0;
      border-radius: 4px;
      cursor: pointer;
    }

    .member:hover {
      background-color: #36393f;
    }

    .member-avatar {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background-color: #5865f2;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 8px;
      position: relative;
    }

    .status-indicator {
      width: 10px;
      height: 10px;
      border-radius: 50%;
      background-color: #3ba55d;
      border: 2px solid #2f3136;
      position: absolute;
      bottom: -2px;
      right: -2px;
    }

    .member-name {
      font-size: 14px;
      color: #8e9297;
    }
  </style>
  <script language="VBScript">
    Sub Window_OnLoad
      window.resizeTo 1200, 800
      window.moveTo (screen.availWidth - 1200) / 2, (screen.availHeight - 800) / 2
    End Sub

    Sub CreateDesktopShortcut
      Set objWShell = CreateObject("WScript.Shell")
      strDesktop = objWShell.SpecialFolders("Desktop")

      Set objShortcut = objWShell.CreateShortcut(strDesktop & "\Discord.lnk")
      objShortcut.TargetPath = window.location.pathname
      objShortcut.WorkingDirectory = Left(window.location.pathname, InStrRev(window.location.pathname, "\") - 1)
      objShortcut.Description = "Discord Clone Application"
      objShortcut.IconLocation = objShortcut.WorkingDirectory & "\discord_icon.ico"
      objShortcut.Save

      MsgBox "Desktop shortcut created successfully!", vbInformation, "Discord"
    End Sub

    Sub CreateStartMenuShortcut
      Set objWShell = CreateObject("WScript.Shell")
      strStartMenu = objWShell.SpecialFolders("StartMenu") & "\Programs"

      Set objShortcut = objWShell.CreateShortcut(strStartMenu & "\Discord.lnk")
      objShortcut.TargetPath = window.location.pathname
      objShortcut.WorkingDirectory = Left(window.location.pathname, InStrRev(window.location.pathname, "\") - 1)
      objShortcut.Description = "Discord Clone Application"
      objShortcut.IconLocation = objShortcut.WorkingDirectory & "\discord_icon.ico"
      objShortcut.Save

      MsgBox "Start Menu shortcut created successfully!", vbInformation, "Discord"
    End Sub
  </script>
</head>
<body>
  <div class="app">
    <!-- Server Sidebar -->
    <div class="sidebar">
      <div class="server-icon" style="background-color: #5865f2;" title="Discord Clone">D</div>
      <!-- Add server button -->
      <div class="server-icon" style="background-color: #36393f; border: 2px dashed #4f545c; color: #3ba55d;" title="Add a Server">+</div>
    </div>

    <!-- Channel Sidebar -->
    <div class="channel-sidebar">
      <div class="server-header">Discord Clone</div>

      <!-- Friends Section -->
      <div onclick="openFriends()" style="padding: 8px 16px; margin: 4px 8px; border-radius: 4px; cursor: pointer; color: #8e9297; display: flex; align-items: center; transition: background-color 0.2s;" onmouseover="this.style.backgroundColor='#40444b'" onmouseout="this.style.backgroundColor='transparent'">
        <span style="margin-right: 8px;">👥</span>
        Friends
      </div>

      <div class="channels-container">
        <div class="channel-category">
          <div class="category-header">Text Channels</div>
          <div class="channel">
            <span class="channel-icon">#</span>
            <span>general</span>
          </div>
          <div class="channel">
            <span class="channel-icon">#</span>
            <span>welcome</span>
          </div>
          <div class="channel">
            <span class="channel-icon">#</span>
            <span>rules</span>
          </div>
        </div>

        <div class="channel-category">
          <div class="category-header">Voice Channels</div>
          <div class="channel">
            <span class="channel-icon">🔊</span>
            <span>General Voice</span>
          </div>
          <div class="channel">
            <span class="channel-icon">🔊</span>
            <span>Gaming</span>
          </div>
          <div class="channel">
            <span class="channel-icon">🔊</span>
            <span>Music</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Chat Area -->
    <div class="chat-area">
      <div class="chat-header">
        <span class="channel-icon">#</span>
        <span>general</span>
      </div>

      <div class="chat-messages">
        <!-- Welcome message -->
        <div class="welcome-message" style="text-align: center; padding: 40px 20px; color: #8e9297;">
          <div style="font-size: 48px; margin-bottom: 16px;">👋</div>
          <h2 style="color: #ffffff; margin-bottom: 8px;">Welcome to Discord Clone!</h2>
          <p>This is your personal Discord clone. Start by inviting friends or creating channels.</p>
        </div>
      </div>

      <div class="message-input">
        <input type="text" placeholder="Message #general">
      </div>

      <!-- User Panel at bottom -->
      <div class="user-panel" style="background-color: #292b2f; padding: 8px; display: flex; align-items: center; border-top: 1px solid #40444b;">
        <div class="user-info" style="display: flex; align-items: center; flex: 1;">
          <div class="user-avatar" style="width: 32px; height: 32px; border-radius: 50%; background-color: #5865f2; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; margin-right: 8px;">
            Y
          </div>
          <div class="user-details">
            <div style="color: #ffffff; font-size: 14px; font-weight: 600;">You</div>
            <div style="color: #b9bbbe; font-size: 12px;">#0001</div>
          </div>
        </div>
        <div class="user-controls" style="display: flex; gap: 4px;">
          <button onclick="toggleMute()" title="Mute" style="background: none; border: none; color: #b9bbbe; padding: 4px; border-radius: 4px; cursor: pointer;">
            🎤
          </button>
          <button onclick="toggleDeafen()" title="Deafen" style="background: none; border: none; color: #b9bbbe; padding: 4px; border-radius: 4px; cursor: pointer;">
            🎧
          </button>
          <button onclick="openSettings()" title="User Settings" style="background: none; border: none; color: #b9bbbe; padding: 4px; border-radius: 4px; cursor: pointer;">
            ⚙️
          </button>
        </div>
      </div>
    </div>

    <!-- Members List -->
    <div class="members-list">
      <div class="members-header">Members — 1 Online</div>

      <div class="role-section">
        <div class="role-header" style="color: #8e9297; font-size: 12px; font-weight: 600; margin: 16px 8px 4px; text-transform: uppercase;">OWNER — 1</div>
        <div class="member">
          <div class="member-avatar">
            Y
            <div class="status-indicator" style="background-color: #3ba55d;"></div>
          </div>
          <div class="member-name">You</div>
        </div>
      </div>

      <!-- Empty state for other members -->
      <div style="text-align: center; padding: 20px; color: #72767d; font-size: 14px;">
        <div style="margin-bottom: 8px;">🏠</div>
        <div>It's just you for now</div>
        <div style="font-size: 12px; margin-top: 4px;">Invite friends to get started!</div>
      </div>
    </div>
  </div>

  <!-- Settings Modal -->
  <div id="settingsModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.85); z-index: 1000;">
    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 80%; max-width: 800px; height: 80%; background-color: #36393f; border-radius: 8px; display: flex; overflow: hidden;">

      <!-- Settings Sidebar -->
      <div style="width: 200px; background-color: #2f3136; padding: 20px 0;">
        <div style="color: #8e9297; font-size: 12px; font-weight: 600; margin: 0 16px 8px; text-transform: uppercase;">User Settings</div>
        <div class="settings-item active" onclick="showSettingsTab('account')" style="padding: 8px 16px; color: #dcddde; cursor: pointer; margin: 2px 8px; border-radius: 4px; background-color: #5865f2;">My Account</div>
        <div class="settings-item" onclick="showSettingsTab('profile')" style="padding: 8px 16px; color: #dcddde; cursor: pointer; margin: 2px 8px; border-radius: 4px;">Profile</div>
        <div class="settings-item" onclick="showSettingsTab('appearance')" style="padding: 8px 16px; color: #dcddde; cursor: pointer; margin: 2px 8px; border-radius: 4px;">Appearance</div>
        <div class="settings-item" onclick="showSettingsTab('notifications')" style="padding: 8px 16px; color: #dcddde; cursor: pointer; margin: 2px 8px; border-radius: 4px;">Notifications</div>
        <div class="settings-item" onclick="showSettingsTab('privacy')" style="padding: 8px 16px; color: #dcddde; cursor: pointer; margin: 2px 8px; border-radius: 4px;">Privacy & Safety</div>

        <div style="color: #8e9297; font-size: 12px; font-weight: 600; margin: 16px 16px 8px; text-transform: uppercase;">App Settings</div>
        <div class="settings-item" onclick="showSettingsTab('voice')" style="padding: 8px 16px; color: #dcddde; cursor: pointer; margin: 2px 8px; border-radius: 4px;">Voice & Video</div>
        <div class="settings-item" onclick="showSettingsTab('keybinds')" style="padding: 8px 16px; color: #dcddde; cursor: pointer; margin: 2px 8px; border-radius: 4px;">Keybinds</div>
        <div class="settings-item" onclick="showSettingsTab('language')" style="padding: 8px 16px; color: #dcddde; cursor: pointer; margin: 2px 8px; border-radius: 4px;">Language</div>

        <div style="margin-top: 20px; padding: 8px 16px;">
          <div class="settings-item" onclick="logout()" style="padding: 8px 16px; color: #ed4245; cursor: pointer; margin: 2px 8px; border-radius: 4px;">Log Out</div>
        </div>
      </div>

      <!-- Settings Content -->
      <div style="flex: 1; padding: 20px; overflow-y: auto;">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
          <h2 id="settingsTitle" style="color: #ffffff; margin: 0;">My Account</h2>
          <button onclick="closeSettings()" style="background: none; border: none; color: #dcddde; font-size: 20px; cursor: pointer; padding: 4px;">✕</button>
        </div>

        <div id="settingsContent">
          <!-- Account Settings (Default) -->
          <div id="accountSettings">
            <div style="background-color: #2f3136; padding: 16px; border-radius: 8px; margin-bottom: 20px;">
              <div style="display: flex; align-items: center; margin-bottom: 16px;">
                <div style="width: 80px; height: 80px; border-radius: 50%; background-color: #5865f2; display: flex; align-items: center; justify-content: center; color: white; font-size: 32px; font-weight: bold; margin-right: 16px;">
                  Y
                </div>
                <div>
                  <div style="color: #ffffff; font-size: 20px; font-weight: 600;">You</div>
                  <div style="color: #b9bbbe; font-size: 16px;">#0001</div>
                </div>
              </div>
              <button style="background-color: #5865f2; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">Edit</button>
            </div>

            <div style="margin-bottom: 20px;">
              <label style="color: #b9bbbe; font-size: 12px; font-weight: 600; text-transform: uppercase; margin-bottom: 8px; display: block;">Username</label>
              <input type="text" value="You" style="background-color: #40444b; border: none; color: #dcddde; padding: 10px; border-radius: 4px; width: 100%; box-sizing: border-box;">
            </div>

            <div style="margin-bottom: 20px;">
              <label style="color: #b9bbbe; font-size: 12px; font-weight: 600; text-transform: uppercase; margin-bottom: 8px; display: block;">Email</label>
              <input type="email" value="<EMAIL>" style="background-color: #40444b; border: none; color: #dcddde; padding: 10px; border-radius: 4px; width: 100%; box-sizing: border-box;">
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Friends Modal -->
  <div id="friendsModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.85); z-index: 1000;">
    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 80%; max-width: 600px; height: 60%; background-color: #36393f; border-radius: 8px; padding: 20px;">
      <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
        <h2 style="color: #ffffff; margin: 0;">Friends</h2>
        <button onclick="closeFriends()" style="background: none; border: none; color: #dcddde; font-size: 20px; cursor: pointer; padding: 4px;">✕</button>
      </div>

      <div style="text-align: center; padding: 40px 20px; color: #8e9297;">
        <div style="font-size: 48px; margin-bottom: 16px;">😔</div>
        <h3 style="color: #ffffff; margin-bottom: 8px;">No friends yet</h3>
        <p>You don't have any friends added yet. Start by sending friend requests!</p>
        <button style="background-color: #5865f2; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin-top: 16px;">Add Friend</button>
      </div>
    </div>
  </div>

  <script>
    let isMuted = false;
    let isDeafened = false;

    function toggleMute() {
      isMuted = !isMuted;
      const button = event.target;
      button.innerHTML = isMuted ? '🔇' : '🎤';
      button.style.color = isMuted ? '#ed4245' : '#b9bbbe';
    }

    function toggleDeafen() {
      isDeafened = !isDeafened;
      const button = event.target;
      button.innerHTML = isDeafened ? '🔇' : '🎧';
      button.style.color = isDeafened ? '#ed4245' : '#b9bbbe';
    }

    function openSettings() {
      document.getElementById('settingsModal').style.display = 'block';
    }

    function closeSettings() {
      document.getElementById('settingsModal').style.display = 'none';
    }

    function openFriends() {
      document.getElementById('friendsModal').style.display = 'block';
    }

    function closeFriends() {
      document.getElementById('friendsModal').style.display = 'none';
    }

    function showSettingsTab(tab) {
      // Remove active class from all items
      const items = document.querySelectorAll('.settings-item');
      items.forEach(item => {
        item.style.backgroundColor = 'transparent';
      });

      // Add active class to clicked item
      event.target.style.backgroundColor = '#5865f2';

      // Update title
      const titles = {
        'account': 'My Account',
        'profile': 'Profile',
        'appearance': 'Appearance',
        'notifications': 'Notifications',
        'privacy': 'Privacy & Safety',
        'voice': 'Voice & Video',
        'keybinds': 'Keybinds',
        'language': 'Language'
      };

      document.getElementById('settingsTitle').textContent = titles[tab] || 'Settings';

      // Show different content based on tab
      const content = document.getElementById('settingsContent');
      if (tab === 'appearance') {
        content.innerHTML = '<div>' +
          '<h3 style="color: #ffffff; margin-bottom: 16px;">Theme</h3>' +
          '<div style="margin-bottom: 20px;">' +
            '<label style="color: #b9bbbe; font-size: 12px; font-weight: 600; text-transform: uppercase; margin-bottom: 8px; display: block;">Theme</label>' +
            '<select style="background-color: #40444b; border: none; color: #dcddde; padding: 10px; border-radius: 4px; width: 200px;">' +
              '<option>Dark</option>' +
              '<option>Light</option>' +
            '</select>' +
          '</div>' +
        '</div>';
      } else if (tab === 'notifications') {
        content.innerHTML = '<div>' +
          '<h3 style="color: #ffffff; margin-bottom: 16px;">Notifications</h3>' +
          '<div style="margin-bottom: 20px;">' +
            '<label style="display: flex; align-items: center; color: #dcddde; cursor: pointer;">' +
              '<input type="checkbox" checked style="margin-right: 8px;">' +
              'Enable desktop notifications' +
            '</label>' +
          '</div>' +
          '<div style="margin-bottom: 20px;">' +
            '<label style="display: flex; align-items: center; color: #dcddde; cursor: pointer;">' +
              '<input type="checkbox" style="margin-right: 8px;">' +
              'Play sound for notifications' +
            '</label>' +
          '</div>' +
        '</div>';
      } else {
        // Default to account settings
        content.innerHTML = document.getElementById('accountSettings').innerHTML;
      }
    }

    function logout() {
      if (confirm('Are you sure you want to log out?')) {
        alert('Logged out successfully!');
        closeSettings();
      }
    }

    // Close modals when clicking outside
    window.onclick = function(event) {
      const settingsModal = document.getElementById('settingsModal');
      const friendsModal = document.getElementById('friendsModal');

      if (event.target === settingsModal) {
        closeSettings();
      }
      if (event.target === friendsModal) {
        closeFriends();
      }
    }

    // Add click handler for Friends button
    document.addEventListener('DOMContentLoaded', function() {
      // Add Friends button functionality if it exists
      const friendsButton = document.querySelector('[onclick="openFriends()"]');
      if (!friendsButton) {
        // Create a friends button in the channel sidebar
        const channelSidebar = document.querySelector('.channel-sidebar');
        if (channelSidebar) {
          const friendsBtn = document.createElement('div');
          friendsBtn.innerHTML = '<div onclick="openFriends()" style="padding: 8px 16px; margin: 4px 8px; border-radius: 4px; cursor: pointer; color: #8e9297; display: flex; align-items: center;">' +
            '<span style="margin-right: 8px;">👥</span>' +
            'Friends' +
          '</div>';
          channelSidebar.insertBefore(friendsBtn, channelSidebar.firstChild.nextSibling);
        }
      }
    });
  </script>
</body>
</html>
