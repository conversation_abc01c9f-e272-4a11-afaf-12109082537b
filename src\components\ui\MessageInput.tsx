import React, { useState, useRef, useEffect } from 'react';
import Tooltip from './Tooltip';

interface MessageInputProps {
  channelName?: string;
  onSendMessage: (content: string) => void;
  onFileUpload?: (files: FileList) => void;
  onTyping?: () => void;
  disabled?: boolean;
  placeholder?: string;
}

const MessageInput: React.FC<MessageInputProps> = ({
  channelName = 'channel',
  onSendMessage,
  onFileUpload,
  onTyping,
  disabled = false,
  placeholder
}) => {
  const [message, setMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const defaultPlaceholder = `Message #${channelName}`;

  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${Math.min(textareaRef.current.scrollHeight, 200)}px`;
    }
  }, [message]);

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setMessage(e.target.value);
    
    // Handle typing indicator
    if (!isTyping && e.target.value.length > 0) {
      setIsTyping(true);
      onTyping?.();
    }
    
    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }
    
    // Set new timeout to stop typing indicator
    typingTimeoutRef.current = setTimeout(() => {
      setIsTyping(false);
    }, 1000);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleSendMessage = () => {
    if (message.trim() && !disabled) {
      onSendMessage(message.trim());
      setMessage('');
      setIsTyping(false);
      
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    }
  };

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      onFileUpload?.(files);
    }
    // Reset the input
    e.target.value = '';
  };

  const openFileDialog = () => {
    fileInputRef.current?.click();
  };

  const openGifPicker = () => {
    // TODO: Implement GIF picker
    console.log('GIF picker clicked');
  };

  const openEmojiPicker = () => {
    // TODO: Implement emoji picker
    console.log('Emoji picker clicked');
  };

  const openGiftPicker = () => {
    // TODO: Implement gift picker
    console.log('Gift picker clicked');
  };

  return (
    <div className="px-4 pb-6">
      <div className="relative">
        <div className="bg-discord-input-bg rounded-lg border border-transparent hover:border-discord-separator focus-within:border-discord-primary transition-colors">
          <div className="flex items-end p-3">
            {/* File upload button */}
            <Tooltip content="Upload a file">
              <button
                onClick={openFileDialog}
                disabled={disabled}
                className="flex-shrink-0 w-6 h-6 mr-3 flex items-center justify-center text-discord-interactive-normal hover:text-discord-interactive-hover transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <svg width="20" height="20" viewBox="0 0 24 24">
                  <path
                    fill="currentColor"
                    d="M12 2C13.1046 2 14 2.89543 14 4V10H20C21.1046 10 22 10.8954 22 12C22 13.1046 21.1046 14 20 14H14V20C14 21.1046 13.1046 22 12 22C10.8954 22 10 21.1046 10 20V14H4C2.89543 14 2 13.1046 2 12C2 10.8954 2.89543 10 4 10H10V4C10 2.89543 10.8954 2 12 2Z"
                  />
                </svg>
              </button>
            </Tooltip>

            {/* Message input */}
            <div className="flex-1 min-w-0">
              <textarea
                ref={textareaRef}
                value={message}
                onChange={handleInputChange}
                onKeyDown={handleKeyDown}
                placeholder={placeholder || defaultPlaceholder}
                disabled={disabled}
                className="w-full bg-transparent text-discord-text-normal placeholder-discord-interactive-muted resize-none focus:outline-none disabled:cursor-not-allowed"
                style={{
                  minHeight: '20px',
                  maxHeight: '200px',
                  lineHeight: '20px'
                }}
                rows={1}
              />
            </div>

            {/* Right side buttons */}
            <div className="flex items-center space-x-2 ml-3">
              {/* Gift button */}
              <Tooltip content="Send a gift">
                <button
                  onClick={openGiftPicker}
                  disabled={disabled}
                  className="flex-shrink-0 w-6 h-6 flex items-center justify-center text-discord-interactive-normal hover:text-discord-interactive-hover transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <svg width="20" height="20" viewBox="0 0 24 24">
                    <path
                      fill="currentColor"
                      d="M12 8.99997H16C16 6.78997 14.21 4.99997 12 4.99997C9.79 4.99997 8 6.78997 8 8.99997H12ZM12 8.99997V12M20 12V20C20 21.1046 19.1046 22 18 22H6C4.89543 22 4 21.1046 4 20V12H20ZM20 12H4V10C4 8.89543 4.89543 8 6 8H18C19.1046 8 20 8.89543 20 10V12Z"
                    />
                  </svg>
                </button>
              </Tooltip>

              {/* GIF button */}
              <Tooltip content="Select a GIF">
                <button
                  onClick={openGifPicker}
                  disabled={disabled}
                  className="flex-shrink-0 w-6 h-6 flex items-center justify-center text-discord-interactive-normal hover:text-discord-interactive-hover transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <svg width="20" height="20" viewBox="0 0 24 24">
                    <path
                      fill="currentColor"
                      d="M2 2V22H22V2H2ZM20 20H4V4H20V20ZM6.5 17.5H9V15H11V13H9V11H11V9H6.5V17.5ZM12.5 17.5H15V15.5H17.5V9H12.5V17.5ZM14.5 11H15.5V13.5H14.5V11Z"
                    />
                  </svg>
                </button>
              </Tooltip>

              {/* Emoji button */}
              <Tooltip content="Select an emoji">
                <button
                  onClick={openEmojiPicker}
                  disabled={disabled}
                  className="flex-shrink-0 w-6 h-6 flex items-center justify-center text-discord-interactive-normal hover:text-discord-interactive-hover transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <svg width="20" height="20" viewBox="0 0 24 24">
                    <path
                      fill="currentColor"
                      d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12C4 7.59 7.59 4 12 4C16.41 4 20 7.59 20 12C20 16.41 16.41 20 12 20ZM8.5 11C9.33 11 10 10.33 10 9.5C10 8.67 9.33 8 8.5 8C7.67 8 7 8.67 7 9.5C7 10.33 7.67 11 8.5 11ZM15.5 11C16.33 11 17 10.33 17 9.5C17 8.67 16.33 8 15.5 8C14.67 8 14 8.67 14 9.5C14 10.33 14.67 11 15.5 11ZM12 17.5C14.33 17.5 16.31 16.04 17 14H7C7.69 16.04 9.67 17.5 12 17.5Z"
                    />
                  </svg>
                </button>
              </Tooltip>

              {/* Send button (only visible when there's text) */}
              {message.trim() && (
                <Tooltip content="Send message">
                  <button
                    onClick={handleSendMessage}
                    disabled={disabled}
                    className="flex-shrink-0 w-8 h-8 flex items-center justify-center bg-discord-primary text-white rounded-full hover:bg-opacity-90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <svg width="16" height="16" viewBox="0 0 24 24">
                      <path
                        fill="currentColor"
                        d="M2.01 21L23 12L2.01 3L2 10L17 12L2 14L2.01 21Z"
                      />
                    </svg>
                  </button>
                </Tooltip>
              )}
            </div>
          </div>
        </div>

        {/* Character count (if needed) */}
        {message.length > 1800 && (
          <div className="absolute bottom-1 right-3 text-xs text-discord-text-muted">
            {2000 - message.length}
          </div>
        )}

        {/* Hidden file input */}
        <input
          ref={fileInputRef}
          type="file"
          multiple
          onChange={handleFileUpload}
          className="hidden"
          accept="image/*,video/*,audio/*,.txt,.pdf,.doc,.docx,.zip,.rar"
        />
      </div>

      {/* Typing indicator placeholder */}
      {/* This would be implemented separately as a typing indicator component */}
    </div>
  );
};

export default MessageInput;
