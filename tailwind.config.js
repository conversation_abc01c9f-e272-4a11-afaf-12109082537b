/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{js,jsx,ts,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        discord: {
          // Primary Colors
          'blurple': '#5865F2',
          'primary': '#5865F2',
          'green': '#57F287',
          'yellow': '#FEE75C',
          'fuchsia': '#EB459E',
          'red': '#ED4245',

          // Background Colors
          'dark': '#36393F',
          'darker': '#2F3136',
          'darkest': '#202225',
          'light': '#40444B',
          'lighter': '#484C52',
          'bg': '#36393F',
          'light-bg': '#2F3136',
          'dark-bg': '#202225',
          'input-bg': '#40444B',

          // Text Colors
          'text-normal': '#DCDDDE',
          'text-muted': '#72767D',
          'text-link': '#00AFF4',
          'header-primary': '#FFFFFF',
          'header-secondary': '#B9BBBE',
          'text': '#DCDDDE',
          'muted': '#72767D',
          'link': '#00AFF4',
          'channel': '#8E9297',
          'channel-selected': '#FFFFFF',

          // Interactive Colors
          'interactive-normal': '#B9BBBE',
          'interactive-hover': '#DCDDDE',
          'interactive-active': '#FFFFFF',
          'interactive-muted': '#4F545C',

          // Status Colors
          'status-online': '#43B581',
          'status-idle': '#FAA61A',
          'status-dnd': '#F04747',
          'status-offline': '#747F8D',
          'status-streaming': '#593695',

          // Additional UI Colors
          'separator': '#2D2F32',
          'modal-bg': '#36393F',
          'tooltip-bg': '#18191C',
          'context-menu-bg': '#18191C',
          'hover-bg': '#32353B',
          'active-bg': '#42464D',
          'mention-bg': '#F04747',
          'unread-indicator': '#FFFFFF',
        },
      },
    },
  },
  plugins: [],
}
