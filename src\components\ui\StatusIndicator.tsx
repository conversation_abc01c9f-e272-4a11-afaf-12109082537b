import React from 'react';

interface StatusIndicatorProps {
  status: 'online' | 'idle' | 'dnd' | 'offline' | 'streaming';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

const StatusIndicator: React.FC<StatusIndicatorProps> = ({
  status,
  size = 'md',
  className = ''
}) => {
  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'w-2.5 h-2.5';
      case 'md':
        return 'w-3 h-3';
      case 'lg':
        return 'w-4 h-4';
      default:
        return 'w-3 h-3';
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'online':
        return 'bg-discord-status-online';
      case 'idle':
        return 'bg-discord-status-idle';
      case 'dnd':
        return 'bg-discord-status-dnd';
      case 'offline':
        return 'bg-discord-status-offline';
      case 'streaming':
        return 'bg-discord-status-streaming';
      default:
        return 'bg-discord-status-offline';
    }
  };

  const getStatusShape = () => {
    if (status === 'idle') {
      // Crescent moon shape for idle
      return (
        <div className={`${getSizeClasses()} ${className} relative`}>
          <div className={`w-full h-full rounded-full ${getStatusColor()}`} />
          <div className="absolute top-0 right-0 w-1/2 h-1/2 bg-discord-darker rounded-full" />
        </div>
      );
    }

    if (status === 'dnd') {
      // Circle with line for do not disturb
      return (
        <div className={`${getSizeClasses()} ${className} relative`}>
          <div className={`w-full h-full rounded-full ${getStatusColor()}`} />
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-2/3 h-0.5 bg-white rounded-full" />
        </div>
      );
    }

    // Default circle for online, offline, and streaming
    return (
      <div className={`${getSizeClasses()} ${className} rounded-full ${getStatusColor()}`} />
    );
  };

  return getStatusShape();
};

export default StatusIndicator;
