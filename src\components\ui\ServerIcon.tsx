import React, { useState } from 'react';

interface ServerIconProps {
  server?: {
    id: string;
    name: string;
    icon?: string;
  };
  isActive?: boolean;
  hasUnread?: boolean;
  mentionCount?: number;
  isHome?: boolean;
  isAddButton?: boolean;
  isExploreButton?: boolean;
  onClick?: () => void;
  className?: string;
}

const ServerIcon: React.FC<ServerIconProps> = ({
  server,
  isActive = false,
  hasUnread = false,
  mentionCount = 0,
  isHome = false,
  isAddButton = false,
  isExploreButton = false,
  onClick,
  className = ''
}) => {
  const [isHovered, setIsHovered] = useState(false);

  const getIconContent = () => {
    if (isHome) {
      return (
        <svg width="28" height="20" viewBox="0 0 28 20" className="text-white">
          <path fill="currentColor" d="M20.6644 20C20.6644 20 19.8014 18.9762 19.0822 18.0714C22.2226 17.1905 23.4212 15.2381 23.4212 15.2381C22.4384 15.881 21.5034 16.3334 20.6644 16.6429C19.4658 17.1429 18.3151 17.4762 17.1884 17.6667C14.887 18.0953 12.7774 17.9762 10.9795 17.6429C9.61301 17.381 8.43836 17 7.45548 16.6191C6.90411 16.4048 6.30137 16.1429 5.69863 15.8096C5.63014 15.7619 5.56164 15.7381 5.49315 15.6905C5.44521 15.6667 5.42123 15.6429 5.39726 15.6191C4.96575 15.381 4.71233 15.2143 4.71233 15.2143C4.71233 15.2143 5.86301 17.1191 8.91781 18.0238C8.19863 18.9286 7.31507 20 7.31507 20C2.0137 19.8333 0 16.381 0 16.381C0 8.7619 3.45205 2.61905 3.45205 2.61905C6.90411 -0.07143 10.1918 0 10.1918 0L10.4452 0.285714C6.08219 1.52381 4.11644 3.33333 4.11644 3.33333C4.11644 3.33333 4.65753 3.04762 5.53425 2.66667C8.10274 1.59524 10.1438 1.2619 10.9795 1.19048C11.1233 1.16667 11.2432 1.14286 11.3871 1.14286C12.8493 0.952381 14.5034 0.904762 16.2329 1.11905C18.5068 1.42857 20.9521 2.14286 23.4452 3.33333C23.4452 3.33333 21.5514 1.61905 17.4247 0.380952L17.7781 0C17.7781 0 21.0658 -0.07143 24.5178 2.61905C24.5178 2.61905 28 8.7619 28 16.381C28 16.381 25.9623 19.8333 20.6644 20ZM9.51712 8.88095C8.15068 8.88095 7.07534 10.0714 7.07534 11.5238C7.07534 12.9762 8.17808 14.1667 9.51712 14.1667C10.8562 14.1667 11.9589 12.9762 11.9589 11.5238C11.9829 10.0714 10.8562 8.88095 9.51712 8.88095ZM18.4589 8.88095C17.0925 8.88095 16.0171 10.0714 16.0171 11.5238C16.0171 12.9762 17.1199 14.1667 18.4589 14.1667C19.798 14.1667 20.9007 12.9762 20.9007 11.5238C20.9007 10.0714 19.798 8.88095 18.4589 8.88095Z"/>
        </svg>
      );
    }

    if (isAddButton) {
      return (
        <svg width="24" height="24" viewBox="0 0 24 24" className="text-discord-green">
          <path fill="currentColor" d="M20 11.1111H12.8889V4H11.1111V11.1111H4V12.8889H11.1111V20H12.8889V12.8889H20V11.1111Z"/>
        </svg>
      );
    }

    if (isExploreButton) {
      return (
        <svg width="24" height="24" viewBox="0 0 24 24" className="text-discord-green">
          <path fill="currentColor" d="M12 10.9C11.39 10.9 10.9 11.39 10.9 12C10.9 12.61 11.39 13.1 12 13.1C12.61 13.1 13.1 12.61 13.1 12C13.1 11.39 12.61 10.9 12 10.9ZM12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM14.19 14.19L6 18L9.81 9.81L18 6L14.19 14.19Z"/>
        </svg>
      );
    }

    if (server?.icon) {
      return (
        <img
          src={server.icon}
          alt={server.name}
          className="w-full h-full object-cover"
        />
      );
    }

    // Default server icon with first letter
    return (
      <span className="text-lg font-medium text-white">
        {server?.name?.charAt(0)?.toUpperCase() || 'S'}
      </span>
    );
  };

  const getBackgroundColor = () => {
    if (isActive) return 'bg-discord-primary';
    if (isHovered && (isAddButton || isExploreButton)) return 'bg-discord-green';
    if (isHovered) return 'bg-discord-primary';
    if (isAddButton || isExploreButton) return 'bg-discord-light';
    return 'bg-discord-light';
  };

  const getBorderRadius = () => {
    if (isActive || isHovered) return 'rounded-2xl';
    return 'rounded-3xl';
  };

  const getPillIndicator = () => {
    if (isActive) {
      return (
        <div className="absolute -left-1 top-1/2 transform -translate-y-1/2 w-1 h-10 bg-white rounded-r-full" />
      );
    }
    
    if (hasUnread || mentionCount > 0) {
      return (
        <div className="absolute -left-1 top-1/2 transform -translate-y-1/2 w-1 h-2 bg-white rounded-r-full" />
      );
    }
    
    return null;
  };

  const getMentionBadge = () => {
    if (mentionCount > 0) {
      return (
        <div className="absolute -top-1 -right-1 min-w-4 h-4 bg-discord-red rounded-full flex items-center justify-center">
          <span className="text-xs font-medium text-white px-1">
            {mentionCount > 99 ? '99+' : mentionCount}
          </span>
        </div>
      );
    }
    return null;
  };

  const getUnreadIndicator = () => {
    if (hasUnread && mentionCount === 0) {
      return (
        <div className="absolute -top-1 -right-1 w-2 h-2 bg-white rounded-full" />
      );
    }
    return null;
  };

  return (
    <div className="relative flex items-center justify-center">
      {getPillIndicator()}
      
      <div
        className={`
          relative w-12 h-12 flex items-center justify-center cursor-pointer
          transition-all duration-100 ease-out
          ${getBorderRadius()}
          ${getBackgroundColor()}
          ${className}
        `}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        onClick={onClick}
      >
        {getIconContent()}
        {getMentionBadge()}
        {getUnreadIndicator()}
      </div>
    </div>
  );
};

export default ServerIcon;
