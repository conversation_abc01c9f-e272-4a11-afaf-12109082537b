<!DOCTYPE html>
<html>
<head>
  <title>Discord</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      margin: 0;
      padding: 0;
      background-color: #36393f;
      color: #dcddde;
      height: 100vh;
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }
    
    .app {
      display: flex;
      height: 100vh;
    }
    
    .sidebar {
      width: 72px;
      background-color: #202225;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 12px 0;
    }
    
    .server-icon {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      background-color: #5865f2;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 8px;
      font-size: 20px;
      cursor: pointer;
      transition: border-radius 0.2s;
    }
    
    .server-icon:hover {
      border-radius: 16px;
    }
    
    .channel-sidebar {
      width: 240px;
      background-color: #2f3136;
      display: flex;
      flex-direction: column;
    }
    
    .server-header {
      padding: 16px;
      border-bottom: 1px solid #202225;
      font-weight: bold;
    }
    
    .channels-container {
      padding: 8px;
      flex: 1;
      overflow-y: auto;
    }
    
    .channel-category {
      margin-bottom: 8px;
    }
    
    .category-header {
      text-transform: uppercase;
      font-size: 12px;
      font-weight: 600;
      color: #8e9297;
      margin-bottom: 4px;
      padding: 0 8px;
    }
    
    .channel {
      display: flex;
      align-items: center;
      padding: 6px 8px;
      border-radius: 4px;
      color: #8e9297;
      cursor: pointer;
    }
    
    .channel:hover {
      background-color: #36393f;
      color: #dcddde;
    }
    
    .channel-icon {
      margin-right: 6px;
      font-size: 20px;
    }
    
    .chat-area {
      flex: 1;
      display: flex;
      flex-direction: column;
    }
    
    .chat-header {
      padding: 12px 16px;
      border-bottom: 1px solid #202225;
      display: flex;
      align-items: center;
    }
    
    .chat-messages {
      flex: 1;
      padding: 16px;
      overflow-y: auto;
    }
    
    .message {
      display: flex;
      margin-bottom: 16px;
    }
    
    .message-avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: #5865f2;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16px;
    }
    
    .message-content {
      flex: 1;
    }
    
    .message-header {
      display: flex;
      align-items: baseline;
      margin-bottom: 4px;
    }
    
    .message-author {
      font-weight: 500;
      margin-right: 8px;
    }
    
    .message-time {
      font-size: 12px;
      color: #8e9297;
    }
    
    .message-text {
      color: #dcddde;
    }
    
    .message-input {
      padding: 16px;
      margin: 0 16px 16px;
      background-color: #40444b;
      border-radius: 8px;
      display: flex;
      align-items: center;
    }
    
    .message-input input {
      flex: 1;
      background: transparent;
      border: none;
      color: #dcddde;
      font-size: 16px;
      outline: none;
    }
    
    .message-input input::placeholder {
      color: #8e9297;
    }
    
    .members-list {
      width: 240px;
      background-color: #2f3136;
      padding: 16px;
      overflow-y: auto;
    }
    
    .members-header {
      text-transform: uppercase;
      font-size: 12px;
      font-weight: 600;
      color: #8e9297;
      margin-bottom: 8px;
    }
    
    .member {
      display: flex;
      align-items: center;
      padding: 6px 0;
      border-radius: 4px;
      cursor: pointer;
    }
    
    .member:hover {
      background-color: #36393f;
    }
    
    .member-avatar {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background-color: #5865f2;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 8px;
      position: relative;
    }
    
    .status-indicator {
      width: 10px;
      height: 10px;
      border-radius: 50%;
      background-color: #3ba55d;
      border: 2px solid #2f3136;
      position: absolute;
      bottom: -2px;
      right: -2px;
    }
    
    .member-name {
      font-size: 14px;
      color: #8e9297;
    }
  </style>
</head>
<body>
  <div class="app">
    <!-- Server Sidebar -->
    <div class="sidebar">
      <div class="server-icon" style="background-color: #5865f2;">D</div>
      <div class="server-icon">G</div>
      <div class="server-icon">M</div>
      <div class="server-icon">A</div>
    </div>
    
    <!-- Channel Sidebar -->
    <div class="channel-sidebar">
      <div class="server-header">Discord Clone</div>
      <div class="channels-container">
        <div class="channel-category">
          <div class="category-header">Text Channels</div>
          <div class="channel">
            <span class="channel-icon">#</span>
            <span>general</span>
          </div>
          <div class="channel">
            <span class="channel-icon">#</span>
            <span>welcome</span>
          </div>
          <div class="channel">
            <span class="channel-icon">#</span>
            <span>rules</span>
          </div>
        </div>
        
        <div class="channel-category">
          <div class="category-header">Voice Channels</div>
          <div class="channel">
            <span class="channel-icon">🔊</span>
            <span>General Voice</span>
          </div>
          <div class="channel">
            <span class="channel-icon">🔊</span>
            <span>Gaming</span>
          </div>
          <div class="channel">
            <span class="channel-icon">🔊</span>
            <span>Music</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Chat Area -->
    <div class="chat-area">
      <div class="chat-header">
        <span class="channel-icon">#</span>
        <span>general</span>
      </div>
      
      <div class="chat-messages">
        <div class="message">
          <div class="message-avatar">J</div>
          <div class="message-content">
            <div class="message-header">
              <div class="message-author">JohnDoe</div>
              <div class="message-time">Today at 12:30 PM</div>
            </div>
            <div class="message-text">Hey everyone! Welcome to the server!</div>
          </div>
        </div>
        
        <div class="message">
          <div class="message-avatar">J</div>
          <div class="message-content">
            <div class="message-header">
              <div class="message-author">JaneSmith</div>
              <div class="message-time">Today at 12:32 PM</div>
            </div>
            <div class="message-text">Thanks for having me here!</div>
          </div>
        </div>
        
        <div class="message">
          <div class="message-avatar">J</div>
          <div class="message-content">
            <div class="message-header">
              <div class="message-author">JohnDoe</div>
              <div class="message-time">Today at 12:35 PM</div>
            </div>
            <div class="message-text">I just added a new bot to help us manage the server.</div>
          </div>
        </div>
        
        <div class="message">
          <div class="message-avatar">D</div>
          <div class="message-content">
            <div class="message-header">
              <div class="message-author">DiscordBot</div>
              <div class="message-time">Today at 12:36 PM</div>
            </div>
            <div class="message-text">Hello! I am DiscordBot. Type !help to see available commands.</div>
          </div>
        </div>
      </div>
      
      <div class="message-input">
        <input type="text" placeholder="Message #general">
      </div>
    </div>
    
    <!-- Members List -->
    <div class="members-list">
      <div class="members-header">Members — 4 Online</div>
      
      <div class="member">
        <div class="member-avatar">
          J
          <div class="status-indicator" style="background-color: #3ba55d;"></div>
        </div>
        <div class="member-name">JohnDoe</div>
      </div>
      
      <div class="member">
        <div class="member-avatar">
          J
          <div class="status-indicator" style="background-color: #faa81a;"></div>
        </div>
        <div class="member-name">JaneSmith</div>
      </div>
      
      <div class="member">
        <div class="member-avatar">
          D
          <div class="status-indicator" style="background-color: #3ba55d;"></div>
        </div>
        <div class="member-name">DiscordBot</div>
      </div>
      
      <div class="member">
        <div class="member-avatar">
          B
          <div class="status-indicator" style="background-color: #ed4245;"></div>
        </div>
        <div class="member-name">BobJohnson</div>
      </div>
    </div>
  </div>
</body>
</html>
