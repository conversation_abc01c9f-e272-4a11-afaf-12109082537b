<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Discord Clone</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <div class="app">
        <!-- 1. Server Sidebar (Far Left) -->
        <div class="server-sidebar">
            <div class="home-button">
                <div class="server-icon active">
                    <i class="fab fa-discord"></i>
                </div>
            </div>
            <div class="server-separator"></div>
            <div class="server-list">
                <div class="server-icon" data-server="gaming">
                    <span>G</span>
                    <div class="unread-indicator"></div>
                </div>
                <div class="server-icon" data-server="music">
                    <span>M</span>
                </div>
                <div class="server-icon" data-server="art">
                    <span>A</span>
                    <div class="mention-indicator">3</div>
                </div>
                <div class="server-icon add-server">
                    <i class="fas fa-plus"></i>
                </div>
                <div class="server-icon explore-servers">
                    <i class="fas fa-compass"></i>
                </div>
            </div>
            <div class="download-apps">
                <div class="server-icon">
                    <i class="fas fa-download"></i>
                </div>
            </div>
        </div>

        <!-- 2. Channel Sidebar (Left-Center) -->
        <div class="channel-sidebar">
            <div class="server-header">
                <h3>Discord Clone</h3>
                <i class="fas fa-chevron-down"></i>
            </div>
            <div class="channels-container">
                <div class="channel-category">
                    <div class="category-header">
                        <i class="fas fa-chevron-down"></i>
                        <span>TEXT CHANNELS</span>
                    </div>
                    <div class="channel active">
                        <span class="channel-icon">#</span>
                        <span class="channel-name">general</span>
                    </div>
                    <div class="channel">
                        <span class="channel-icon">#</span>
                        <span class="channel-name">welcome</span>
                        <div class="unread-indicator"></div>
                    </div>
                    <div class="channel">
                        <span class="channel-icon">#</span>
                        <span class="channel-name">rules</span>
                    </div>
                    <div class="channel">
                        <span class="channel-icon">#</span>
                        <span class="channel-name">announcements</span>
                        <div class="mention-indicator">2</div>
                    </div>
                </div>
                <div class="channel-category">
                    <div class="category-header">
                        <i class="fas fa-chevron-down"></i>
                        <span>VOICE CHANNELS</span>
                    </div>
                    <div class="channel">
                        <span class="channel-icon"><i class="fas fa-volume-up"></i></span>
                        <span class="channel-name">General Voice</span>
                    </div>
                    <div class="channel">
                        <span class="channel-icon"><i class="fas fa-volume-up"></i></span>
                        <span class="channel-name">Gaming</span>
                    </div>
                    <div class="channel">
                        <span class="channel-icon"><i class="fas fa-volume-up"></i></span>
                        <span class="channel-name">Music</span>
                    </div>
                </div>
            </div>
            <div class="user-area">
                <div class="user-info">
                    <div class="user-avatar">
                        <img src="img/avatar.png" alt="User Avatar" onerror="this.src='https://via.placeholder.com/32'">
                        <div class="status-indicator online"></div>
                    </div>
                    <div class="user-details">
                        <div class="username">Username</div>
                        <div class="user-tag">#1234</div>
                    </div>
                </div>
                <div class="user-controls">
                    <button class="control-button" title="Mute"><i class="fas fa-microphone"></i></button>
                    <button class="control-button" title="Deafen"><i class="fas fa-headphones"></i></button>
                    <button class="control-button" title="Settings"><i class="fas fa-cog"></i></button>
                </div>
            </div>
        </div>

        <!-- 3. Chat Area (Center) -->
        <div class="chat-area">
            <div class="chat-header">
                <div class="channel-info">
                    <span class="channel-icon">#</span>
                    <span class="channel-name">general</span>
                </div>
                <div class="channel-topic">
                    <span>Welcome to the general chat | Be respectful to others</span>
                </div>
                <div class="header-controls">
                    <button class="control-button" title="Notifications"><i class="fas fa-bell"></i></button>
                    <button class="control-button" title="Pinned Messages"><i class="fas fa-thumbtack"></i></button>
                    <button class="control-button" title="Hide Member List"><i class="fas fa-users"></i></button>
                    <div class="search-bar">
                        <input type="text" placeholder="Search">
                        <i class="fas fa-search"></i>
                    </div>
                    <button class="control-button" title="Inbox"><i class="fas fa-inbox"></i></button>
                    <button class="control-button" title="Help"><i class="fas fa-question-circle"></i></button>
                </div>
            </div>
            <div class="chat-messages">
                <div class="message">
                    <div class="message-avatar">
                        <img src="img/avatar1.png" alt="User Avatar" onerror="this.src='https://via.placeholder.com/40'">
                    </div>
                    <div class="message-content">
                        <div class="message-header">
                            <div class="message-author">JohnDoe</div>
                            <div class="message-time">Today at 12:30 PM</div>
                        </div>
                        <div class="message-text">Hey everyone! Welcome to the server!</div>
                    </div>
                </div>
                <div class="message">
                    <div class="message-avatar">
                        <img src="img/avatar2.png" alt="User Avatar" onerror="this.src='https://via.placeholder.com/40'">
                    </div>
                    <div class="message-content">
                        <div class="message-header">
                            <div class="message-author">SarahSmith</div>
                            <div class="message-time">Today at 12:32 PM</div>
                        </div>
                        <div class="message-text">Thanks for having me here! Looking forward to chatting with everyone.</div>
                    </div>
                </div>
                <div class="message">
                    <div class="message-avatar">
                        <img src="img/avatar3.png" alt="User Avatar" onerror="this.src='https://via.placeholder.com/40'">
                    </div>
                    <div class="message-content">
                        <div class="message-header">
                            <div class="message-author">TechGuru</div>
                            <div class="message-time">Today at 12:35 PM</div>
                        </div>
                        <div class="message-text">I just found this awesome article about web development: <a href="#">https://example.com/web-dev-tips</a></div>
                    </div>
                </div>
                <div class="message">
                    <div class="message-avatar">
                        <img src="img/avatar4.png" alt="User Avatar" onerror="this.src='https://via.placeholder.com/40'">
                    </div>
                    <div class="message-content">
                        <div class="message-header">
                            <div class="message-author">GamingPro</div>
                            <div class="message-time">Today at 12:40 PM</div>
                        </div>
                        <div class="message-text">Anyone up for some gaming later tonight? I was thinking of starting a stream around 8PM.</div>
                    </div>
                </div>
                <div class="message">
                    <div class="message-avatar">
                        <img src="img/avatar1.png" alt="User Avatar" onerror="this.src='https://via.placeholder.com/40'">
                    </div>
                    <div class="message-content">
                        <div class="message-header">
                            <div class="message-author">JohnDoe</div>
                            <div class="message-time">Today at 12:42 PM</div>
                        </div>
                        <div class="message-text">I'm in! What game are we playing?</div>
                    </div>
                </div>
            </div>
            <div class="message-input">
                <button class="attachment-button" title="Attach File">
                    <i class="fas fa-plus-circle"></i>
                </button>
                <input type="text" placeholder="Message #general">
                <div class="input-controls">
                    <button class="control-button" title="Gift"><i class="fas fa-gift"></i></button>
                    <button class="control-button" title="GIF"><i class="fas fa-film"></i></button>
                    <button class="control-button" title="Emoji"><i class="far fa-smile"></i></button>
                </div>
            </div>
        </div>

        <!-- 4. Member List (Right) -->
        <div class="member-list">
            <div class="members-header">MEMBERS—24 ONLINE</div>
            <div class="role-category">
                <div class="role-name">ADMIN—1</div>
                <div class="member">
                    <div class="member-avatar">
                        <img src="img/avatar1.png" alt="User Avatar" onerror="this.src='https://via.placeholder.com/32'">
                        <div class="status-indicator online"></div>
                    </div>
                    <div class="member-name">JohnDoe</div>
                </div>
            </div>
            <div class="role-category">
                <div class="role-name">MODERATORS—3</div>
                <div class="member">
                    <div class="member-avatar">
                        <img src="img/avatar2.png" alt="User Avatar" onerror="this.src='https://via.placeholder.com/32'">
                        <div class="status-indicator idle"></div>
                    </div>
                    <div class="member-name">SarahSmith</div>
                </div>
                <div class="member">
                    <div class="member-avatar">
                        <img src="img/avatar3.png" alt="User Avatar" onerror="this.src='https://via.placeholder.com/32'">
                        <div class="status-indicator online"></div>
                    </div>
                    <div class="member-name">TechGuru</div>
                </div>
                <div class="member">
                    <div class="member-avatar">
                        <img src="img/avatar5.png" alt="User Avatar" onerror="this.src='https://via.placeholder.com/32'">
                        <div class="status-indicator dnd"></div>
                    </div>
                    <div class="member-name">ModeratorMax</div>
                </div>
            </div>
            <div class="role-category">
                <div class="role-name">ONLINE—20</div>
                <div class="member">
                    <div class="member-avatar">
                        <img src="img/avatar4.png" alt="User Avatar" onerror="this.src='https://via.placeholder.com/32'">
                        <div class="status-indicator online"></div>
                    </div>
                    <div class="member-name">GamingPro</div>
                </div>
                <div class="member">
                    <div class="member-avatar">
                        <img src="img/avatar6.png" alt="User Avatar" onerror="this.src='https://via.placeholder.com/32'">
                        <div class="status-indicator online"></div>
                    </div>
                    <div class="member-name">ArtisticSoul</div>
                </div>
                <div class="member">
                    <div class="member-avatar">
                        <img src="img/avatar7.png" alt="User Avatar" onerror="this.src='https://via.placeholder.com/32'">
                        <div class="status-indicator streaming"></div>
                    </div>
                    <div class="member-name">StreamerGuy</div>
                </div>
                <!-- More members would be here -->
            </div>
            <div class="role-category">
                <div class="role-name">OFFLINE—10</div>
                <div class="member">
                    <div class="member-avatar">
                        <img src="img/avatar8.png" alt="User Avatar" onerror="this.src='https://via.placeholder.com/32'">
                        <div class="status-indicator offline"></div>
                    </div>
                    <div class="member-name">OfflineUser1</div>
                </div>
                <div class="member">
                    <div class="member-avatar">
                        <img src="img/avatar9.png" alt="User Avatar" onerror="this.src='https://via.placeholder.com/32'">
                        <div class="status-indicator offline"></div>
                    </div>
                    <div class="member-name">OfflineUser2</div>
                </div>
                <!-- More offline members would be here -->
            </div>
        </div>
    </div>

    <!-- 5. User Settings Panel (Modal) - Hidden by default -->
    <div class="settings-modal" style="display: none;">
        <div class="settings-container">
            <div class="settings-sidebar">
                <div class="settings-category active">User Settings</div>
                <div class="settings-category">Profile</div>
                <div class="settings-category">Privacy & Safety</div>
                <div class="settings-category">Authorized Apps</div>
                <div class="settings-category">Connections</div>
                <div class="settings-category">Appearance</div>
                <div class="settings-category">Accessibility</div>
                <div class="settings-category">Voice & Video</div>
                <div class="settings-category">Text & Images</div>
                <div class="settings-category">Notifications</div>
                <div class="settings-category">Keybinds</div>
                <div class="settings-category">Language</div>
                <div class="settings-category">Streamer Mode</div>
                <div class="settings-category">Advanced</div>
                <div class="settings-category logout">Log Out</div>
            </div>
            <div class="settings-content">
                <div class="settings-header">
                    <h2>User Settings</h2>
                    <button class="close-button"><i class="fas fa-times"></i></button>
                </div>
                <div class="settings-body">
                    <!-- Settings content would go here -->
                </div>
            </div>
        </div>
    </div>

    <script src="js/script.js"></script>
</body>
</html>
