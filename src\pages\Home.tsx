import React from 'react';
import { Link } from 'react-router-dom';
import ServerList from '../components/ServerList';
import DirectMessages from '../components/DirectMessages';

const Home: React.FC = () => {
  return (
    <div className="flex h-screen bg-discord-bg">
      <ServerList />
      <DirectMessages />
      <div className="flex-1 flex flex-col">
        <div className="p-4 shadow-md bg-discord-light-bg">
          <h1 className="text-2xl font-bold text-white">Home</h1>
        </div>
        <div className="flex-1 p-6 overflow-y-auto">
          <div className="max-w-2xl mx-auto text-center">
            <h2 className="text-3xl font-bold text-white mb-4">Welcome to Discord Clone!</h2>
            <p className="text-discord-text mb-8">
              This is a simplified version of Discord. You can create servers, channels, and send messages.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="p-6 bg-discord-light-bg rounded-lg hover:bg-opacity-80 transition-colors">
                <h3 className="text-xl font-semibold text-white mb-2">Find Your Servers</h3>
                <p className="text-discord-muted">
                  Check out your servers in the sidebar on the left
                </p>
              </div>
              <div className="p-6 bg-discord-light-bg rounded-lg hover:bg-opacity-80 transition-colors">
                <h3 className="text-xl font-semibold text-white mb-2">Start Messaging</h3>
                <p className="text-discord-muted">
                  Send messages in channels or direct messages
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Home;
