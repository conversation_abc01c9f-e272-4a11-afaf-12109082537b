import { supabase } from '../lib/supabase';
import { Server, ServerMember } from '../types';

/**
 * Get all servers the current user is a member of
 */
export const getUserServers = async (): Promise<Server[]> => {
  const { data: serverMembers, error: memberError } = await supabase
    .from('server_members')
    .select('server_id')
    .eq('user_id', supabase.auth.getUser().then(({ data }) => data.user?.id));

  if (memberError) {
    console.error('Error fetching server memberships:', memberError);
    return [];
  }

  if (!serverMembers || serverMembers.length === 0) {
    return [];
  }

  const serverIds = serverMembers.map(member => member.server_id);

  const { data: servers, error: serverError } = await supabase
    .from('servers')
    .select(`
      id,
      name,
      icon,
      owner_id,
      created_at,
      updated_at
    `)
    .in('id', serverIds);

  if (serverError) {
    console.error('Error fetching servers:', serverError);
    return [];
  }

  return servers || [];
};

/**
 * Get a server by ID
 */
export const getServer = async (serverId: string): Promise<Server | null> => {
  const { data, error } = await supabase
    .from('servers')
    .select(`
      id,
      name,
      icon,
      owner_id,
      created_at,
      updated_at
    `)
    .eq('id', serverId)
    .single();

  if (error) {
    console.error('Error fetching server:', error);
    return null;
  }

  return data;
};

/**
 * Create a new server
 */
export const createServer = async (name: string, icon?: string): Promise<Server | null> => {
  const { data: userData } = await supabase.auth.getUser();
  
  if (!userData.user) {
    console.error('User not authenticated');
    return null;
  }

  const { data, error } = await supabase
    .from('servers')
    .insert({
      name,
      icon: icon || name.charAt(0).toUpperCase(),
      owner_id: userData.user.id
    })
    .select()
    .single();

  if (error) {
    console.error('Error creating server:', error);
    return null;
  }

  // Add the creator as a member of the server
  const { error: memberError } = await supabase
    .from('server_members')
    .insert({
      server_id: data.id,
      user_id: userData.user.id
    });

  if (memberError) {
    console.error('Error adding user to server:', memberError);
    // We could delete the server here, but let's just return it anyway
  }

  // Create a default "general" channel
  const { error: channelError } = await supabase
    .from('channels')
    .insert({
      server_id: data.id,
      name: 'general',
      type: 'text',
      position: 0
    });

  if (channelError) {
    console.error('Error creating default channel:', channelError);
  }

  return data;
};

/**
 * Update a server
 */
export const updateServer = async (
  serverId: string,
  updates: { name?: string; icon?: string }
): Promise<Server | null> => {
  const { data, error } = await supabase
    .from('servers')
    .update(updates)
    .eq('id', serverId)
    .select()
    .single();

  if (error) {
    console.error('Error updating server:', error);
    return null;
  }

  return data;
};

/**
 * Delete a server
 */
export const deleteServer = async (serverId: string): Promise<boolean> => {
  const { error } = await supabase
    .from('servers')
    .delete()
    .eq('id', serverId);

  if (error) {
    console.error('Error deleting server:', error);
    return false;
  }

  return true;
};

/**
 * Get all members of a server
 */
export const getServerMembers = async (serverId: string): Promise<ServerMember[]> => {
  const { data, error } = await supabase
    .from('server_members')
    .select(`
      id,
      server_id,
      user_id,
      nickname,
      joined_at,
      profiles:user_id (
        id,
        username,
        discriminator,
        avatar,
        status
      )
    `)
    .eq('server_id', serverId);

  if (error) {
    console.error('Error fetching server members:', error);
    return [];
  }

  return data || [];
};

/**
 * Join a server
 */
export const joinServer = async (serverId: string): Promise<boolean> => {
  const { data: userData } = await supabase.auth.getUser();
  
  if (!userData.user) {
    console.error('User not authenticated');
    return false;
  }

  const { error } = await supabase
    .from('server_members')
    .insert({
      server_id: serverId,
      user_id: userData.user.id
    });

  if (error) {
    console.error('Error joining server:', error);
    return false;
  }

  return true;
};

/**
 * Leave a server
 */
export const leaveServer = async (serverId: string): Promise<boolean> => {
  const { data: userData } = await supabase.auth.getUser();
  
  if (!userData.user) {
    console.error('User not authenticated');
    return false;
  }

  const { error } = await supabase
    .from('server_members')
    .delete()
    .eq('server_id', serverId)
    .eq('user_id', userData.user.id);

  if (error) {
    console.error('Error leaving server:', error);
    return false;
  }

  return true;
};
