import React, { useState } from 'react';

// Mock data for messages
const mockMessages = [
  {
    id: '1',
    content: 'Hey everyone! Welcome to the server!',
    timestamp: '2023-05-20T12:30:00Z',
    author: {
      id: '1',
      username: '<PERSON><PERSON><PERSON>',
      avatar: '👨',
      isBot: false,
      roles: ['Admin']
    }
  },
  {
    id: '2',
    content: 'Thanks for having me here!',
    timestamp: '2023-05-20T12:32:00Z',
    author: {
      id: '2',
      username: '<PERSON><PERSON><PERSON>',
      avatar: '👩',
      isBot: false,
      roles: ['Member']
    }
  },
  {
    id: '3',
    content: 'I just added a new bot to help us manage the server.',
    timestamp: '2023-05-20T12:35:00Z',
    author: {
      id: '1',
      username: '<PERSON><PERSON><PERSON>',
      avatar: '👨',
      isBot: false,
      roles: ['Admin']
    }
  },
  {
    id: '4',
    content: 'Hello! I am DiscordBot. Type !help to see available commands.',
    timestamp: '2023-05-20T12:36:00Z',
    author: {
      id: '3',
      username: '<PERSON>rd<PERSON><PERSON>',
      avatar: '🤖',
      isBot: true,
      roles: ['Bot']
    }
  },
  {
    id: '5',
    content: 'This is a really cool server! I\'m excited to be part of this community.',
    timestamp: '2023-05-20T12:40:00Z',
    author: {
      id: '4',
      username: 'BobJohnson',
      avatar: '👨‍🦰',
      isBot: false,
      roles: ['Member']
    }
  }
];

interface ChatAreaProps {
  channelId?: string;
}

const ChatArea: React.FC<ChatAreaProps> = ({ channelId }) => {
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState(mockMessages);

  // Format timestamp
  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // Format date for message groups
  const formatMessageDate = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleDateString([], { month: 'long', day: 'numeric', year: 'numeric' });
  };

  // Handle message submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (message.trim() === '') return;
    
    const newMessage = {
      id: Date.now().toString(),
      content: message,
      timestamp: new Date().toISOString(),
      author: {
        id: 'current-user',
        username: 'You',
        avatar: 'U',
        isBot: false,
        roles: ['Member']
      }
    };
    
    setMessages([...messages, newMessage]);
    setMessage('');
  };

  // Group messages by author and date
  const groupedMessages = messages.reduce((groups: any[], message, index) => {
    const previousMessage = messages[index - 1];
    
    // Check if this is a new day compared to the previous message
    if (index === 0 || formatMessageDate(message.timestamp) !== formatMessageDate(previousMessage.timestamp)) {
      groups.push({
        type: 'date-divider',
        date: formatMessageDate(message.timestamp),
        id: `date-${message.timestamp}`
      });
    }
    
    // Check if this message should be grouped with the previous one
    const shouldGroup = 
      previousMessage && 
      previousMessage.author.id === message.author.id &&
      formatMessageDate(message.timestamp) === formatMessageDate(previousMessage.timestamp) &&
      new Date(message.timestamp).getTime() - new Date(previousMessage.timestamp).getTime() < 5 * 60 * 1000; // 5 minutes
    
    if (shouldGroup) {
      // Add to the previous group
      const lastGroup = groups[groups.length - 1];
      if (lastGroup.type === 'message-group') {
        lastGroup.messages.push(message);
      } else {
        // Create a new group
        groups.push({
          type: 'message-group',
          author: message.author,
          messages: [message],
          id: `group-${message.id}`
        });
      }
    } else {
      // Create a new group
      groups.push({
        type: 'message-group',
        author: message.author,
        messages: [message],
        id: `group-${message.id}`
      });
    }
    
    return groups;
  }, []);

  return (
    <div className="flex-1 flex flex-col bg-discord-bg">
      {/* Channel header */}
      <div className="p-4 shadow-md bg-discord-light-bg flex items-center">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5 mr-2 text-discord-muted">
          <path fillRule="evenodd" d="M4.848 2.771A49.144 49.144 0 0112 2.25c2.43 0 4.817.178 7.152.52 1.978.292 3.348 2.024 3.348 3.97v6.02c0 1.946-1.37 3.678-3.348 3.97a48.901 48.901 0 01-3.476.383.39.39 0 00-.297.17l-2.755 4.133a.75.75 0 01-1.248 0l-2.755-4.133a.39.39 0 00-.297-.17 48.9 48.9 0 01-3.476-.384c-1.978-.29-3.348-2.024-3.348-3.97V6.741c0-1.946 1.37-3.68 3.348-3.97zM6.75 8.25a.75.75 0 01.75-.75h9a.75.75 0 010 1.5h-9a.75.75 0 01-.75-.75zm.75 2.25a.75.75 0 000 1.5H12a.75.75 0 000-1.5H7.5z" clipRule="evenodd" />
        </svg>
        <h2 className="font-bold text-white">general</h2>
        <div className="mx-2 text-discord-muted">|</div>
        <p className="text-sm text-discord-muted">Welcome to the general channel!</p>
        
        <div className="ml-auto flex space-x-4">
          <button className="text-discord-muted hover:text-discord-text">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5">
              <path d="M5.85 3.5a.75.75 0 00-1.117-1 9.719 9.719 0 00-2.348 4.876.75.75 0 001.479.248A8.219 8.219 0 015.85 3.5zM19.267 2.5a.75.75 0 10-1.118 1 8.22 8.22 0 011.987 *********** 0 001.48-.248A9.72 9.72 0 0019.266 2.5z" />
              <path fillRule="evenodd" d="M12 2.25A6.75 6.75 0 005.25 9v.75a8.217 8.217 0 01-2.119 ********** 0 00.298 1.206c1.544.57 3.16.99 4.831 1.243a3.75 3.75 0 107.48 0 24.583 24.583 0 004.83-*********** 0 00.298-1.205 8.217 8.217 0 01-2.118-5.52V9A6.75 6.75 0 0012 2.25zM9.75 18c0-.034 0-.067.002-.1a25.05 25.05 0 004.496 0l.002.1a2.25 2.25 0 11-4.5 0z" clipRule="evenodd" />
            </svg>
          </button>
          <button className="text-discord-muted hover:text-discord-text">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5">
              <path fillRule="evenodd" d="M4.5 12a1.5 1.5 0 113 0 1.5 1.5 0 01-3 0zm6 0a1.5 1.5 0 113 0 1.5 1.5 0 01-3 0zm6 0a1.5 1.5 0 113 0 1.5 1.5 0 01-3 0z" clipRule="evenodd" />
            </svg>
          </button>
          <button className="text-discord-muted hover:text-discord-text">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5">
              <path fillRule="evenodd" d="M10.5 3.75a6.75 6.75 0 100 13.5 6.75 6.75 0 000-13.5zM2.25 10.5a8.25 8.25 0 1114.59 5.28l4.69 4.69a.75.75 0 11-1.06 1.06l-4.69-4.69A8.25 8.25 0 012.25 10.5z" clipRule="evenodd" />
            </svg>
          </button>
          <button className="text-discord-muted hover:text-discord-text">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5">
              <path fillRule="evenodd" d="M19.902 4.098a3.75 3.75 0 00-5.304 0l-4.5 4.5a3.75 3.75 0 001.035 *********** 0 01-.646 1.353 5.25 5.25 0 01-1.449-8.45l4.5-4.5a5.25 5.25 0 117.424 7.424l-1.757 1.757a.75.75 0 11-1.06-1.06l1.757-1.757a3.75 3.75 0 000-5.304zm-7.389 4.267a.75.75 0 011-.353 5.25 5.25 0 011.449 8.45l-4.5 4.5a5.25 5.25 0 11-7.424-7.424l1.757-1.757a.75.75 0 111.06 1.06l-1.757 1.757a3.75 3.75 0 105.304 5.304l4.5-4.5a3.75 3.75 0 00-1.035-*********** 0 01-.354-1z" clipRule="evenodd" />
            </svg>
          </button>
        </div>
      </div>
      
      {/* Messages area */}
      <div className="flex-1 overflow-y-auto p-4">
        {groupedMessages.map(group => {
          if (group.type === 'date-divider') {
            return (
              <div key={group.id} className="flex items-center my-4">
                <div className="flex-1 h-px bg-discord-separator"></div>
                <div className="px-4 text-xs font-semibold text-discord-muted">{group.date}</div>
                <div className="flex-1 h-px bg-discord-separator"></div>
              </div>
            );
          }
          
          return (
            <div key={group.id} className="mb-4 group hover:bg-discord-light-bg -mx-2 p-2 rounded">
              <div className="flex">
                <div className="w-10 h-10 rounded-full bg-discord-primary flex items-center justify-center text-white mr-4">
                  {group.author.avatar}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center">
                    <span className="font-medium text-white">{group.author.username}</span>
                    {group.author.isBot && (
                      <span className="ml-2 text-xs bg-discord-primary text-white px-1 rounded">BOT</span>
                    )}
                    <span className="ml-2 text-xs text-discord-muted">
                      {formatTimestamp(group.messages[0].timestamp)}
                    </span>
                  </div>
                  
                  {group.messages.map((message: any) => (
                    <div key={message.id} className="mt-1 text-discord-text">
                      {message.content}
                    </div>
                  ))}
                </div>
                
                <div className="hidden group-hover:flex space-x-2 text-discord-muted">
                  <button className="hover:text-discord-text">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5">
                      <path d="M11.645 20.91l-.007-.003-.022-.012a15.247 15.247 0 01-.383-.218 25.18 25.18 0 01-4.244-3.17C4.688 15.36 2.25 12.174 2.25 8.25 2.25 5.322 4.714 3 7.688 3A5.5 5.5 0 0112 5.052 5.5 5.5 0 0116.313 3c2.973 0 5.437 2.322 5.437 5.25 0 3.925-2.438 7.111-4.739 9.256a25.175 25.175 0 01-4.244 3.17 15.247 15.247 0 01-.383.219l-.022.012-.007.004-.003.001a.752.752 0 01-.704 0l-.003-.001z" />
                    </svg>
                  </button>
                  <button className="hover:text-discord-text">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5">
                      <path fillRule="evenodd" d="M4.5 12a1.5 1.5 0 113 0 1.5 1.5 0 01-3 0zm6 0a1.5 1.5 0 113 0 1.5 1.5 0 01-3 0zm6 0a1.5 1.5 0 113 0 1.5 1.5 0 01-3 0z" clipRule="evenodd" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          );
        })}
      </div>
      
      {/* Message input */}
      <div className="p-4">
        <form onSubmit={handleSubmit} className="bg-discord-input-bg rounded-lg p-2">
          <div className="flex items-center">
            <button type="button" className="text-discord-muted hover:text-discord-text p-2">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-6 h-6">
                <path fillRule="evenodd" d="M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25zm-2.625 6c-.54 0-.828.419-.936.634a1.96 1.96 0 00-.189.866c0 .298.059.605.189.866.108.215.395.634.936.634.54 0 .828-.419.936-.634.13-.26.189-.568.189-.866 0-.298-.059-.605-.189-.866-.108-.215-.395-.634-.936-.634zm4.314.634c.108-.215.395-.634.936-.634.54 0 .828.419.936.634.13.26.189.568.189.866 0 .298-.059.605-.189.866-.108.215-.395.634-.936.634-.54 0-.828-.419-.936-.634a1.96 1.96 0 01-.189-.866c0-.298.059-.605.189-.866zm2.023 6.828a.75.75 0 10-1.06-1.06 3.75 3.75 0 01-5.304 0 .75.75 0 00-1.06 1.06 5.25 5.25 0 007.424 0z" clipRule="evenodd" />
              </svg>
            </button>
            
            <input
              type="text"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder="Message #general"
              className="flex-1 bg-transparent text-discord-text px-4 py-2 focus:outline-none"
            />
            
            <button type="button" className="text-discord-muted hover:text-discord-text p-2">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-6 h-6">
                <path fillRule="evenodd" d="M18.97 3.659a2.25 2.25 0 00-3.182 0l-10.94 10.94a3.75 3.75 0 105.304 5.303l7.693-7.693a.75.75 0 011.06 1.06l-7.693 7.693a5.25 5.25 0 11-7.424-7.424l10.939-10.94a3.75 3.75 0 115.303 5.304L9.097 18.835l-.008.008-.007.007-.002.002-.003.002A2.25 2.25 0 015.91 15.66l7.81-7.81a.75.75 0 011.061 1.06l-7.81 7.81a.75.75 0 001.054 1.068L18.97 6.84a2.25 2.25 0 000-3.182z" clipRule="evenodd" />
              </svg>
            </button>
            
            <button type="button" className="text-discord-muted hover:text-discord-text p-2">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-6 h-6">
                <path fillRule="evenodd" d="M4.5 3.75a3 3 0 00-3 3v10.5a3 3 0 003 3h15a3 3 0 003-3V6.75a3 3 0 00-3-3h-15zm9 4.5a.75.75 0 00-1.5 0v7.5a.75.75 0 001.5 0v-7.5zm1.5 0a.75.75 0 01.75-.75h3a.75.75 0 010 1.5h-3a.75.75 0 01-.75-.75zm-6 0a.75.75 0 01.75-.75h3a.75.75 0 010 1.5h-3a.75.75 0 01-.75-.75z" clipRule="evenodd" />
              </svg>
            </button>
            
            <button type="button" className="text-discord-muted hover:text-discord-text p-2">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-6 h-6">
                <path d="M12 9a3.75 3.75 0 100 7.5A3.75 3.75 0 0012 9z" />
                <path fillRule="evenodd" d="M9.344 3.071a49.52 49.52 0 015.312 0c.967.052 1.83.585 2.332 1.39l.821 1.317c.24.383.645.643 1.11.71.386.054.77.113 1.152.177 1.432.239 2.429 1.493 2.429 2.909V18a3 3 0 01-3 3h-15a3 3 0 01-3-3V9.574c0-1.416.997-2.67 2.429-2.909.382-.064.766-.123 1.151-.178a1.56 1.56 0 001.11-.71l.822-1.315a2.942 2.942 0 012.332-1.39zM6.75 12.75a5.25 5.25 0 1110.5 0 5.25 5.25 0 01-10.5 0zm12-1.5a.75.75 0 100 ********* 0 000-1.5z" clipRule="evenodd" />
              </svg>
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ChatArea;
