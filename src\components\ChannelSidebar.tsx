import React, { useState } from 'react';
import { Link, useParams } from 'react-router-dom';

// Mock data for servers
const servers = [
  { 
    id: '1', 
    name: 'General Server', 
    categories: [
      {
        id: 'cat1',
        name: 'TEXT CHANNELS',
        channels: [
          { id: '1', name: 'general', type: 'text', unread: false, mentions: 0 },
          { id: '2', name: 'welcome', type: 'text', unread: true, mentions: 0 },
          { id: '3', name: 'rules', type: 'text', unread: false, mentions: 0 },
        ]
      },
      {
        id: 'cat2',
        name: 'VOICE CHANNELS',
        channels: [
          { id: '4', name: 'General Voice', type: 'voice', users: ['User1', 'User2'] },
          { id: '5', name: 'Gaming', type: 'voice', users: [] },
          { id: '6', name: 'Music', type: 'voice', users: ['User3'] },
        ]
      }
    ]
  },
  { 
    id: '2', 
    name: 'Gaming Server', 
    categories: [
      {
        id: 'cat1',
        name: 'TEXT CHANNELS',
        channels: [
          { id: '1', name: 'general', type: 'text', unread: false, mentions: 0 },
          { id: '2', name: 'minecraft', type: 'text', unread: true, mentions: 2 },
          { id: '3', name: 'valorant', type: 'text', unread: false, mentions: 0 },
        ]
      },
      {
        id: 'cat2',
        name: 'VOICE CHANNELS',
        channels: [
          { id: '4', name: 'General Voice', type: 'voice', users: [] },
          { id: '5', name: 'Team 1', type: 'voice', users: [] },
          { id: '6', name: 'Team 2', type: 'voice', users: [] },
        ]
      }
    ]
  },
];

interface ChannelSidebarProps {
  serverId?: string;
}

const ChannelSidebar: React.FC<ChannelSidebarProps> = ({ serverId }) => {
  const { channelId } = useParams<{ channelId: string }>();
  const [expandedCategories, setExpandedCategories] = useState<Record<string, boolean>>({});
  
  // Find the current server
  const currentServer = servers.find(server => server.id === serverId) || servers[0];

  // Toggle category expansion
  const toggleCategory = (categoryId: string) => {
    setExpandedCategories(prev => ({
      ...prev,
      [categoryId]: !prev[categoryId]
    }));
  };

  // Check if a channel is active
  const isChannelActive = (id: string) => channelId === id;

  return (
    <div className="w-60 bg-discord-light-bg flex flex-col">
      {/* Server header */}
      <div className="p-4 shadow-md flex items-center justify-between cursor-pointer hover:bg-discord-bg">
        <h2 className="font-bold text-white truncate">{currentServer.name}</h2>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5 text-discord-muted">
          <path fillRule="evenodd" d="M12.53 16.28a.75.75 0 01-1.06 0l-7.5-7.5a.75.75 0 011.06-1.06L12 14.69l6.97-6.97a.75.75 0 111.06 1.06l-7.5 7.5z" clipRule="evenodd" />
        </svg>
      </div>

      {/* Channels list */}
      <div className="flex-1 overflow-y-auto px-2 py-4">
        {currentServer.categories.map(category => (
          <div key={category.id} className="mb-4">
            {/* Category header */}
            <div 
              className="flex items-center px-1 mb-1 cursor-pointer"
              onClick={() => toggleCategory(category.id)}
            >
              <svg 
                xmlns="http://www.w3.org/2000/svg" 
                viewBox="0 0 24 24" 
                fill="currentColor" 
                className={`w-3 h-3 text-discord-muted mr-1 transition-transform ${expandedCategories[category.id] ? 'rotate-90' : ''}`}
              >
                <path fillRule="evenodd" d="M4.5 5.653c0-1.426 1.529-2.33 2.779-1.643l11.54 6.348c1.295.712 1.295 2.573 0 3.285L7.28 19.991c-1.25.687-2.779-.217-2.779-1.643V5.653z" clipRule="evenodd" />
              </svg>
              <h3 className="text-xs font-semibold text-discord-muted tracking-wider">{category.name}</h3>
            </div>

            {/* Channels */}
            <div className={expandedCategories[category.id] === false ? 'hidden' : ''}>
              {category.channels.map(channel => (
                <Link
                  key={channel.id}
                  to={`/channels/${serverId}/${channel.id}`}
                  className={`flex items-center px-2 py-1 rounded mb-1 group ${
                    isChannelActive(channel.id) 
                      ? 'bg-discord-bg text-discord-channel-selected' 
                      : 'text-discord-channel hover:bg-discord-bg hover:text-discord-channel-selected'
                  }`}
                >
                  {/* Channel icon */}
                  {channel.type === 'text' ? (
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5 mr-2 opacity-70">
                      <path fillRule="evenodd" d="M4.848 2.771A49.144 49.144 0 0112 2.25c2.43 0 4.817.178 7.152.52 1.978.292 3.348 2.024 3.348 3.97v6.02c0 1.946-1.37 3.678-3.348 3.97a48.901 48.901 0 01-3.476.383.39.39 0 00-.297.17l-2.755 4.133a.75.75 0 01-1.248 0l-2.755-4.133a.39.39 0 00-.297-.17 48.9 48.9 0 01-3.476-.384c-1.978-.29-3.348-2.024-3.348-3.97V6.741c0-1.946 1.37-3.68 3.348-3.97zM6.75 8.25a.75.75 0 01.75-.75h9a.75.75 0 010 1.5h-9a.75.75 0 01-.75-.75zm.75 2.25a.75.75 0 000 1.5H12a.75.75 0 000-1.5H7.5z" clipRule="evenodd" />
                    </svg>
                  ) : (
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5 mr-2 opacity-70">
                      <path d="M8.25 4.5a3.75 3.75 0 117.5 0v8.25a3.75 3.75 0 11-7.5 0V4.5z" />
                      <path d="M6 10.5a.75.75 0 01.75.75v1.5a5.25 5.25 0 1010.5 0v-1.5a.75.75 0 011.5 0v1.5a6.751 6.751 0 01-6 6.709v2.291h3a.75.75 0 010 1.5h-7.5a.75.75 0 010-1.5h3v-2.291a6.751 6.751 0 01-6-6.709v-1.5A.75.75 0 016 10.5z" />
                    </svg>
                  )}
                  
                  {/* Channel name */}
                  <span className="flex-1 truncate">{channel.name}</span>
                  
                  {/* Channel indicators */}
                  {channel.type === 'text' && channel.unread && channel.mentions > 0 && (
                    <span className="bg-discord-red text-white text-xs font-bold rounded-full px-1.5 min-w-[18px] text-center">
                      {channel.mentions}
                    </span>
                  )}
                  
                  {/* Channel actions */}
                  <div className="hidden group-hover:flex space-x-1 ml-1">
                    <button className="text-discord-muted hover:text-discord-text">
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-4 h-4">
                        <path d="M6.25 6.375a4.125 4.125 0 118.25 0 4.125 4.125 0 01-8.25 0zM3.25 19.125a7.125 7.125 0 0114.25 0v.003l-.001.119a.75.75 0 01-.363.63 13.067 13.067 0 01-6.761 1.873c-2.472 0-4.786-.684-6.76-1.873a.75.75 0 01-.364-.63l-.001-.122zM19.75 7.5a.75.75 0 00-1.5 0v2.25H16a.75.75 0 000 1.5h2.25v2.25a.75.75 0 001.5 0v-2.25H22a.75.75 0 000-1.5h-2.25V7.5z" />
                      </svg>
                    </button>
                    <button className="text-discord-muted hover:text-discord-text">
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-4 h-4">
                        <path fillRule="evenodd" d="M11.078 2.25c-.917 0-1.699.663-1.85 1.567L9.05 4.889c-.02.12-.115.26-.297.348a7.493 7.493 0 00-.986.57c-.166.115-.334.126-.45.083L6.3 5.508a1.875 1.875 0 00-2.282.819l-.922 1.597a1.875 1.875 0 00.432 2.385l.84.692c.095.078.17.229.154.43a7.598 7.598 0 000 1.139c.015.2-.059.352-.153.43l-.841.692a1.875 1.875 0 00-.432 2.385l.922 1.597a1.875 1.875 0 002.282.818l1.019-.382c.115-.043.283-.031.45.082.312.214.641.405.985.57.182.088.277.228.297.35l.178 1.071c.151.904.933 1.567 1.85 1.567h1.844c.916 0 1.699-.663 1.85-1.567l.178-1.072c.02-.12.114-.26.297-.349.344-.165.673-.356.985-.57.167-.114.335-.125.45-.082l1.02.382a1.875 1.875 0 002.28-.819l.923-1.597a1.875 1.875 0 00-.432-2.385l-.84-.692c-.095-.078-.17-.229-.154-.43a7.614 7.614 0 000-1.139c-.016-.2.059-.352.153-.43l.84-.692c.708-.582.891-1.59.433-2.385l-.922-1.597a1.875 1.875 0 00-2.282-.818l-1.02.382c-.114.043-.282.031-.449-.083a7.49 7.49 0 00-.985-.57c-.183-.087-.277-.227-.297-.348l-.179-1.072a1.875 1.875 0 00-1.85-1.567h-1.843zM12 15.75a3.75 3.75 0 100-7.5 3.75 3.75 0 000 7.5z" clipRule="evenodd" />
                      </svg>
                    </button>
                  </div>
                </Link>
              ))}
            </div>
          </div>
        ))}
      </div>

      {/* User panel */}
      <div className="p-2 bg-discord-dark-bg mt-auto flex items-center">
        <div className="relative mr-2">
          <div className="w-8 h-8 rounded-full bg-discord-primary flex items-center justify-center text-white">
            U
          </div>
          <div className="absolute bottom-0 right-0 w-3 h-3 rounded-full bg-discord-green border-2 border-discord-dark-bg"></div>
        </div>
        <div className="flex-1 min-w-0">
          <div className="text-sm font-medium text-white truncate">Username</div>
          <div className="text-xs text-discord-muted truncate">#1234</div>
        </div>
        <div className="flex space-x-1">
          <button className="text-discord-muted hover:text-discord-text">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5">
              <path d="M8.25 4.5a3.75 3.75 0 117.5 0v8.25a3.75 3.75 0 11-7.5 0V4.5z" />
              <path d="M6 10.5a.75.75 0 01.75.75v1.5a5.25 5.25 0 1010.5 0v-1.5a.75.75 0 011.5 0v1.5a6.751 6.751 0 01-6 6.709v2.291h3a.75.75 0 010 1.5h-7.5a.75.75 0 010-1.5h3v-2.291a6.751 6.751 0 01-6-6.709v-1.5A.75.75 0 016 10.5z" />
            </svg>
          </button>
          <button className="text-discord-muted hover:text-discord-text">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5">
              <path d="M11.645 20.91l-.007-.003-.022-.012a15.247 15.247 0 01-.383-.218 25.18 25.18 0 01-4.244-3.17C4.688 15.36 2.25 12.174 2.25 8.25 2.25 5.322 4.714 3 7.688 3A5.5 5.5 0 0112 5.052 5.5 5.5 0 0116.313 3c2.973 0 5.437 2.322 5.437 5.25 0 3.925-2.438 7.111-4.739 9.256a25.175 25.175 0 01-4.244 3.17 15.247 15.247 0 01-.383.219l-.022.012-.007.004-.003.001a.752.752 0 01-.704 0l-.003-.001z" />
            </svg>
          </button>
          <button className="text-discord-muted hover:text-discord-text">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5">
              <path fillRule="evenodd" d="M11.078 2.25c-.917 0-1.699.663-1.85 1.567L9.05 4.889c-.02.12-.115.26-.297.348a7.493 7.493 0 00-.986.57c-.166.115-.334.126-.45.083L6.3 5.508a1.875 1.875 0 00-2.282.819l-.922 1.597a1.875 1.875 0 00.432 2.385l.84.692c.095.078.17.229.154.43a7.598 7.598 0 000 1.139c.015.2-.059.352-.153.43l-.841.692a1.875 1.875 0 00-.432 2.385l.922 1.597a1.875 1.875 0 002.282.818l1.019-.382c.115-.043.283-.031.45.082.312.214.641.405.985.57.182.088.277.228.297.35l.178 1.071c.151.904.933 1.567 1.85 1.567h1.844c.916 0 1.699-.663 1.85-1.567l.178-1.072c.02-.12.114-.26.297-.349.344-.165.673-.356.985-.57.167-.114.335-.125.45-.082l1.02.382a1.875 1.875 0 002.28-.819l.923-1.597a1.875 1.875 0 00-.432-2.385l-.84-.692c-.095-.078-.17-.229-.154-.43a7.614 7.614 0 000-1.139c-.016-.2.059-.352.153-.43l.84-.692c.708-.582.891-1.59.433-2.385l-.922-1.597a1.875 1.875 0 00-2.282-.818l-1.02.382c-.114.043-.282.031-.449-.083a7.49 7.49 0 00-.985-.57c-.183-.087-.277-.227-.297-.348l-.179-1.072a1.875 1.875 0 00-1.85-1.567h-1.843zM12 15.75a3.75 3.75 0 100-7.5 3.75 3.75 0 000 7.5z" clipRule="evenodd" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
};

export default ChannelSidebar;
