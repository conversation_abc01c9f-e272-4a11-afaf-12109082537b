import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { User } from '../types';
import { supabase } from '../lib/supabase';
import { AuthError, Session, User as SupabaseUser } from '@supabase/supabase-js';
import { getProfile, profileToUser } from '../services/profileService';

interface AuthContextType {
  currentUser: User | null;
  loading: boolean;
  login: (email: string, password: string) => Promise<void>;
  register: (email: string, username: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  // Get user profile from Supabase
  const getUserProfile = async (user: SupabaseUser): Promise<User> => {
    const profile = await getProfile(user.id);

    if (profile) {
      return profileToUser(profile);
    }

    // Fallback if profile not found
    return {
      id: user.id,
      username: user.user_metadata?.username || user.email?.split('@')[0] || 'User',
      discriminator: user.user_metadata?.discriminator || '0000',
      avatar: user.user_metadata?.avatar || user.email?.charAt(0).toUpperCase() || 'U',
      status: 'online',
    };
  };

  useEffect(() => {
    // Set up auth state listener
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        setLoading(true);

        if (session && session.user) {
          const userProfile = await getUserProfile(session.user);
          setCurrentUser(userProfile);
        } else {
          setCurrentUser(null);
        }

        setLoading(false);
      }
    );

    // Check current session
    const checkUser = async () => {
      const { data: { session } } = await supabase.auth.getSession();

      if (session && session.user) {
        const userProfile = await getUserProfile(session.user);
        setCurrentUser(userProfile);
      }

      setLoading(false);
    };

    checkUser();

    // Cleanup subscription
    return () => {
      subscription.unsubscribe();
    };
  }, []);

  const login = async (email: string, password: string): Promise<void> => {
    try {
      setLoading(true);

      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        throw error;
      }

      // Auth state listener will update the user
    } catch (error) {
      setLoading(false);
      throw error;
    }
  };

  const register = async (email: string, username: string, password: string): Promise<void> => {
    try {
      setLoading(true);

      // Generate a random 4-digit discriminator
      const discriminator = Math.floor(1000 + Math.random() * 9000).toString();

      const { error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            username,
            discriminator,
            avatar: username.charAt(0).toUpperCase(),
          },
        },
      });

      if (error) {
        throw error;
      }

      // Auth state listener will update the user
    } catch (error) {
      setLoading(false);
      throw error;
    }
  };

  const logout = async (): Promise<void> => {
    try {
      const { error } = await supabase.auth.signOut();

      if (error) {
        throw error;
      }

      // Auth state listener will update the user
    } catch (error) {
      throw error;
    }
  };

  const value = {
    currentUser,
    loading,
    login,
    register,
    logout,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);

  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }

  return context;
};
