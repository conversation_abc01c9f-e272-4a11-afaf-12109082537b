import { supabase } from '../lib/supabase';
import { User } from '../types';

export interface Profile {
  id: string;
  username: string;
  discriminator: string;
  avatar: string;
  status: 'online' | 'idle' | 'dnd' | 'offline';
  custom_status?: string;
  created_at: string;
  updated_at: string;
}

/**
 * Get a user's profile by ID
 */
export const getProfile = async (userId: string): Promise<Profile | null> => {
  const { data, error } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', userId)
    .single();

  if (error) {
    console.error('Error fetching profile:', error);
    return null;
  }

  return data;
};

/**
 * Update a user's profile
 */
export const updateProfile = async (
  userId: string,
  updates: Partial<Omit<Profile, 'id' | 'created_at' | 'updated_at'>>
): Promise<Profile | null> => {
  const { data, error } = await supabase
    .from('profiles')
    .update(updates)
    .eq('id', userId)
    .select()
    .single();

  if (error) {
    console.error('Error updating profile:', error);
    return null;
  }

  return data;
};

/**
 * Update a user's status
 */
export const updateStatus = async (
  userId: string,
  status: 'online' | 'idle' | 'dnd' | 'offline',
  customStatus?: string
): Promise<Profile | null> => {
  const updates: Partial<Profile> = { status };
  
  if (customStatus !== undefined) {
    updates.custom_status = customStatus;
  }

  return updateProfile(userId, updates);
};

/**
 * Convert a Supabase profile to our User type
 */
export const profileToUser = (profile: Profile): User => {
  return {
    id: profile.id,
    username: profile.username,
    discriminator: profile.discriminator,
    avatar: profile.avatar,
    status: profile.status,
    customStatus: profile.custom_status,
  };
};

/**
 * Get all users
 */
export const getAllProfiles = async (): Promise<Profile[]> => {
  const { data, error } = await supabase
    .from('profiles')
    .select('*')
    .order('username');

  if (error) {
    console.error('Error fetching profiles:', error);
    return [];
  }

  return data || [];
};
