/* Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: "Whitney", "Helvetica Neue", Helvetica, Arial, sans-serif;
    background-color: #36393F;
    color: #DCDDDE;
    height: 100vh;
    overflow: hidden;
}

a {
    color: #00AFF4;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

button {
    background: none;
    border: none;
    cursor: pointer;
    color: inherit;
}

.app {
    display: flex;
    height: 100vh;
}

/* 1. Server Sidebar */
.server-sidebar {
    width: 72px;
    background-color: #202225;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 12px 0;
    height: 100vh;
}

.home-button {
    margin-bottom: 8px;
}

.server-separator {
    width: 32px;
    height: 2px;
    background-color: #36393F;
    margin: 8px 0;
}

.server-list {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex-grow: 1;
    overflow-y: auto;
    width: 100%;
    padding: 0 12px;
}

.server-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background-color: #36393F;
    color: #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 8px;
    font-size: 20px;
    cursor: pointer;
    position: relative;
    transition: border-radius 0.2s ease;
}

.server-icon:hover {
    border-radius: 16px;
    background-color: #5865F2;
}

.server-icon.active {
    border-radius: 16px;
    background-color: #5865F2;
}

.unread-indicator {
    position: absolute;
    left: -4px;
    top: 50%;
    transform: translateY(-50%);
    width: 8px;
    height: 8px;
    background-color: #FFFFFF;
    border-radius: 4px;
}

.mention-indicator {
    position: absolute;
    bottom: -4px;
    right: -4px;
    background-color: #ED4245;
    color: #FFFFFF;
    font-size: 12px;
    min-width: 16px;
    height: 16px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 4px;
}

.add-server {
    background-color: #36393F;
    color: #3BA55D;
}

.add-server:hover {
    background-color: #3BA55D;
    color: #FFFFFF;
}

.explore-servers {
    background-color: #36393F;
    color: #3BA55D;
}

.explore-servers:hover {
    background-color: #3BA55D;
    color: #FFFFFF;
}

.download-apps {
    margin-top: auto;
    padding-bottom: 10px;
}

/* 2. Channel Sidebar */
.channel-sidebar {
    width: 240px;
    background-color: #2F3136;
    display: flex;
    flex-direction: column;
    height: 100vh;
}

.server-header {
    padding: 16px;
    border-bottom: 1px solid #202225;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.server-header h3 {
    font-size: 16px;
    font-weight: bold;
    color: #FFFFFF;
}

.channels-container {
    flex-grow: 1;
    overflow-y: auto;
    padding: 8px 0;
}

.channel-category {
    margin-bottom: 8px;
    padding: 0 8px;
}

.category-header {
    display: flex;
    align-items: center;
    text-transform: uppercase;
    font-size: 12px;
    font-weight: 600;
    color: #8E9297;
    margin-bottom: 4px;
    padding: 8px 0;
    cursor: pointer;
}

.category-header i {
    margin-right: 4px;
    font-size: 10px;
}

.channel {
    display: flex;
    align-items: center;
    padding: 6px 8px;
    border-radius: 4px;
    color: #8E9297;
    cursor: pointer;
    margin-bottom: 2px;
    position: relative;
}

.channel:hover {
    background-color: #36393F;
    color: #DCDDDE;
}

.channel.active {
    background-color: #393C43;
    color: #FFFFFF;
}

.channel-icon {
    margin-right: 6px;
    font-size: 20px;
}

.channel-name {
    font-size: 14px;
}

.user-area {
    height: 52px;
    background-color: #292B2F;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 8px;
}

.user-info {
    display: flex;
    align-items: center;
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    margin-right: 8px;
    position: relative;
    overflow: hidden;
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.status-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    border: 2px solid #292B2F;
    position: absolute;
    bottom: 0;
    right: 0;
}

.status-indicator.online {
    background-color: #3BA55D;
}

.status-indicator.idle {
    background-color: #FAA81A;
}

.status-indicator.dnd {
    background-color: #ED4245;
}

.status-indicator.streaming {
    background-color: #593695;
}

.status-indicator.offline {
    background-color: #747F8D;
}

.user-details {
    display: flex;
    flex-direction: column;
}

.username {
    font-size: 14px;
    font-weight: 500;
    color: #FFFFFF;
}

.user-tag {
    font-size: 12px;
    color: #B9BBBE;
}

.user-controls {
    display: flex;
}

.control-button {
    width: 32px;
    height: 32px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #B9BBBE;
    margin-left: 4px;
}

.control-button:hover {
    background-color: #36393F;
    color: #DCDDDE;
}

/* 3. Chat Area */
.chat-area {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    height: 100vh;
}

.chat-header {
    height: 48px;
    padding: 0 16px;
    border-bottom: 1px solid #202225;
    display: flex;
    align-items: center;
}

.channel-info {
    display: flex;
    align-items: center;
    font-weight: 600;
    color: #FFFFFF;
}

.channel-topic {
    margin-left: 12px;
    font-size: 14px;
    color: #8E9297;
    flex-grow: 1;
}

.header-controls {
    display: flex;
    align-items: center;
}

.search-bar {
    display: flex;
    align-items: center;
    background-color: #202225;
    border-radius: 4px;
    padding: 0 8px;
    margin: 0 8px;
}

.search-bar input {
    background: transparent;
    border: none;
    color: #DCDDDE;
    height: 28px;
    width: 144px;
    outline: none;
}

.search-bar i {
    color: #8E9297;
    margin-left: 4px;
}

.chat-messages {
    flex-grow: 1;
    padding: 16px;
    overflow-y: auto;
}

.message {
    display: flex;
    margin-bottom: 16px;
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 16px;
    overflow: hidden;
}

.message-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.message-content {
    flex-grow: 1;
}

.message-header {
    display: flex;
    align-items: baseline;
    margin-bottom: 4px;
}

.message-author {
    font-weight: 500;
    color: #FFFFFF;
    margin-right: 8px;
}

.message-time {
    font-size: 12px;
    color: #8E9297;
}

.message-text {
    font-size: 15px;
    line-height: 1.3;
    color: #DCDDDE;
}

.message-input {
    margin: 0 16px 24px;
    background-color: #40444B;
    border-radius: 8px;
    display: flex;
    align-items: center;
    padding: 0 16px;
}

.attachment-button {
    color: #B9BBBE;
    margin-right: 16px;
    font-size: 24px;
}

.message-input input {
    flex-grow: 1;
    background: transparent;
    border: none;
    color: #DCDDDE;
    height: 44px;
    font-size: 16px;
    outline: none;
}

.input-controls {
    display: flex;
}

/* 4. Member List */
.member-list {
    width: 240px;
    background-color: #2F3136;
    padding: 16px 8px;
    overflow-y: auto;
    height: 100vh;
}

.members-header {
    text-transform: uppercase;
    font-size: 12px;
    font-weight: 600;
    color: #8E9297;
    margin-bottom: 8px;
    padding: 0 8px;
}

.role-category {
    margin-bottom: 16px;
}

.role-name {
    text-transform: uppercase;
    font-size: 12px;
    color: #8E9297;
    margin-bottom: 4px;
    padding: 0 8px;
}

.member {
    display: flex;
    align-items: center;
    padding: 6px 8px;
    border-radius: 4px;
    cursor: pointer;
}

.member:hover {
    background-color: #36393F;
}

.member-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    margin-right: 8px;
    position: relative;
    overflow: hidden;
}

.member-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.member-name {
    font-size: 14px;
    color: #8E9297;
}

/* 5. Settings Modal */
.settings-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.85);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.settings-container {
    width: 960px;
    height: 80vh;
    background-color: #36393F;
    border-radius: 8px;
    display: flex;
    overflow: hidden;
}

.settings-sidebar {
    width: 218px;
    background-color: #2F3136;
    padding: 60px 6px 60px 20px;
    overflow-y: auto;
}

.settings-category {
    padding: 6px 10px;
    margin-bottom: 2px;
    border-radius: 4px;
    font-size: 16px;
    color: #B9BBBE;
    cursor: pointer;
}

.settings-category:hover {
    background-color: #36393F;
    color: #DCDDDE;
}

.settings-category.active {
    background-color: #393C43;
    color: #FFFFFF;
}

.settings-category.logout {
    color: #ED4245;
}

.settings-content {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.settings-header {
    padding: 60px 40px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.settings-header h2 {
    color: #FFFFFF;
    font-size: 20px;
    font-weight: 600;
}

.close-button {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #B9BBBE;
    font-size: 24px;
}

.close-button:hover {
    background-color: #2F3136;
    color: #FFFFFF;
}

.settings-body {
    flex-grow: 1;
    padding: 0 40px 80px;
    overflow-y: auto;
}

/* Responsive Styles */
@media (max-width: 1200px) {
    .member-list {
        display: none;
    }
}

@media (max-width: 768px) {
    .channel-sidebar {
        display: none;
    }
}
