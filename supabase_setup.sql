-- Create profiles table
CREATE TABLE public.profiles (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  username TEXT NOT NULL,
  discriminator TEXT NOT NULL,
  avatar TEXT,
  status TEXT DEFAULT 'offline',
  custom_status TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Enable Row Level Security
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- Create policies
-- Allow users to view all profiles
CREATE POLICY "Allow users to view all profiles"
  ON public.profiles
  FOR SELECT
  USING (true);

-- Allow authenticated users to insert their own profile
CREATE POLICY "Allow authenticated users to insert their own profile"
  ON public.profiles
  FOR INSERT
  WITH CHECK (auth.uid() = id);

-- Allow authenticated users to update their own profile
CREATE POLICY "Allow authenticated users to update their own profile"
  ON public.profiles
  FOR UPDATE
  USING (auth.uid() = id);

-- Create a function to handle new user signups
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, username, discriminator, avatar)
  VALUES (
    NEW.id,
    COALESCE(NEW.raw_user_meta_data->>'username', split_part(NEW.email, '@', 1)),
    COALESCE(NEW.raw_user_meta_data->>'discriminator', '0000'),
    COALESCE(NEW.raw_user_meta_data->>'avatar', upper(left(COALESCE(NEW.raw_user_meta_data->>'username', split_part(NEW.email, '@', 1)), 1)))
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a trigger to automatically create a profile for new users
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Create a function to update the updated_at column
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a trigger to automatically update the updated_at column
CREATE TRIGGER update_profiles_updated_at
  BEFORE UPDATE ON public.profiles
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

-- Create servers table
CREATE TABLE public.servers (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  icon TEXT,
  owner_id UUID REFERENCES auth.users(id) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Enable Row Level Security for servers
ALTER TABLE public.servers ENABLE ROW LEVEL SECURITY;

-- Create server_members table (for users in servers)
CREATE TABLE public.server_members (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  server_id UUID REFERENCES public.servers(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  nickname TEXT,
  joined_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  UNIQUE(server_id, user_id)
);

-- Enable Row Level Security for server_members
ALTER TABLE public.server_members ENABLE ROW LEVEL SECURITY;

-- Create channels table
CREATE TABLE public.channels (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  server_id UUID REFERENCES public.servers(id) ON DELETE CASCADE NOT NULL,
  name TEXT NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('text', 'voice', 'announcement', 'forum', 'stage')),
  topic TEXT,
  position INTEGER DEFAULT 0 NOT NULL,
  parent_id UUID REFERENCES public.channels(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Enable Row Level Security for channels
ALTER TABLE public.channels ENABLE ROW LEVEL SECURITY;

-- Create messages table
CREATE TABLE public.messages (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  channel_id UUID REFERENCES public.channels(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  content TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  edited BOOLEAN DEFAULT false NOT NULL,
  reply_to_id UUID REFERENCES public.messages(id) ON DELETE SET NULL
);

-- Enable Row Level Security for messages
ALTER TABLE public.messages ENABLE ROW LEVEL SECURITY;

-- Create message_attachments table
CREATE TABLE public.message_attachments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  message_id UUID REFERENCES public.messages(id) ON DELETE CASCADE NOT NULL,
  file_name TEXT NOT NULL,
  file_size INTEGER NOT NULL,
  file_type TEXT NOT NULL,
  url TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Enable Row Level Security for message_attachments
ALTER TABLE public.message_attachments ENABLE ROW LEVEL SECURITY;

-- Create message_reactions table
CREATE TABLE public.message_reactions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  message_id UUID REFERENCES public.messages(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  emoji TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  UNIQUE(message_id, user_id, emoji)
);

-- Enable Row Level Security for message_reactions
ALTER TABLE public.message_reactions ENABLE ROW LEVEL SECURITY;

-- Create direct_messages table
CREATE TABLE public.direct_messages (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  sender_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  recipient_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  content TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  edited BOOLEAN DEFAULT false NOT NULL,
  read BOOLEAN DEFAULT false NOT NULL
);

-- Enable Row Level Security for direct_messages
ALTER TABLE public.direct_messages ENABLE ROW LEVEL SECURITY;

-- Create triggers for updated_at columns
CREATE TRIGGER update_servers_updated_at
  BEFORE UPDATE ON public.servers
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_channels_updated_at
  BEFORE UPDATE ON public.channels
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_messages_updated_at
  BEFORE UPDATE ON public.messages
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_direct_messages_updated_at
  BEFORE UPDATE ON public.direct_messages
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

-- Create RLS policies for servers
CREATE POLICY "Allow users to view servers they are members of"
  ON public.servers
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM public.server_members
      WHERE server_members.server_id = servers.id
      AND server_members.user_id = auth.uid()
    )
    OR
    owner_id = auth.uid()
  );

CREATE POLICY "Allow users to create servers"
  ON public.servers
  FOR INSERT
  WITH CHECK (owner_id = auth.uid());

CREATE POLICY "Allow server owners to update their servers"
  ON public.servers
  FOR UPDATE
  USING (owner_id = auth.uid());

CREATE POLICY "Allow server owners to delete their servers"
  ON public.servers
  FOR DELETE
  USING (owner_id = auth.uid());

-- Create RLS policies for server_members
CREATE POLICY "Allow users to view members of servers they are in"
  ON public.server_members
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM public.server_members AS sm
      WHERE sm.server_id = server_members.server_id
      AND sm.user_id = auth.uid()
    )
  );

CREATE POLICY "Allow server owners to add members"
  ON public.server_members
  FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.servers
      WHERE servers.id = server_members.server_id
      AND servers.owner_id = auth.uid()
    )
    OR
    user_id = auth.uid() -- Allow users to join servers themselves
  );

CREATE POLICY "Allow users to leave servers"
  ON public.server_members
  FOR DELETE
  USING (user_id = auth.uid());

-- Create RLS policies for channels
CREATE POLICY "Allow users to view channels in servers they are members of"
  ON public.channels
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM public.server_members
      WHERE server_members.server_id = channels.server_id
      AND server_members.user_id = auth.uid()
    )
  );

CREATE POLICY "Allow server owners to create channels"
  ON public.channels
  FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.servers
      WHERE servers.id = channels.server_id
      AND servers.owner_id = auth.uid()
    )
  );

CREATE POLICY "Allow server owners to update channels"
  ON public.channels
  FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM public.servers
      WHERE servers.id = channels.server_id
      AND servers.owner_id = auth.uid()
    )
  );

CREATE POLICY "Allow server owners to delete channels"
  ON public.channels
  FOR DELETE
  USING (
    EXISTS (
      SELECT 1 FROM public.servers
      WHERE servers.id = channels.server_id
      AND servers.owner_id = auth.uid()
    )
  );

-- Create RLS policies for messages
CREATE POLICY "Allow users to view messages in channels they have access to"
  ON public.messages
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM public.channels
      JOIN public.server_members ON server_members.server_id = channels.server_id
      WHERE channels.id = messages.channel_id
      AND server_members.user_id = auth.uid()
    )
  );

CREATE POLICY "Allow users to create messages in channels they have access to"
  ON public.messages
  FOR INSERT
  WITH CHECK (
    user_id = auth.uid() AND
    EXISTS (
      SELECT 1 FROM public.channels
      JOIN public.server_members ON server_members.server_id = channels.server_id
      WHERE channels.id = messages.channel_id
      AND server_members.user_id = auth.uid()
    )
  );

CREATE POLICY "Allow users to update their own messages"
  ON public.messages
  FOR UPDATE
  USING (user_id = auth.uid());

CREATE POLICY "Allow users to delete their own messages"
  ON public.messages
  FOR DELETE
  USING (user_id = auth.uid());

-- Create RLS policies for message_attachments
CREATE POLICY "Allow users to view attachments in messages they can see"
  ON public.message_attachments
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM public.messages
      JOIN public.channels ON channels.id = messages.channel_id
      JOIN public.server_members ON server_members.server_id = channels.server_id
      WHERE messages.id = message_attachments.message_id
      AND server_members.user_id = auth.uid()
    )
  );

CREATE POLICY "Allow users to add attachments to their messages"
  ON public.message_attachments
  FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.messages
      WHERE messages.id = message_attachments.message_id
      AND messages.user_id = auth.uid()
    )
  );

-- Create RLS policies for message_reactions
CREATE POLICY "Allow users to view reactions on messages they can see"
  ON public.message_reactions
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM public.messages
      JOIN public.channels ON channels.id = messages.channel_id
      JOIN public.server_members ON server_members.server_id = channels.server_id
      WHERE messages.id = message_reactions.message_id
      AND server_members.user_id = auth.uid()
    )
  );

CREATE POLICY "Allow users to add reactions to messages they can see"
  ON public.message_reactions
  FOR INSERT
  WITH CHECK (
    user_id = auth.uid() AND
    EXISTS (
      SELECT 1 FROM public.messages
      JOIN public.channels ON channels.id = messages.channel_id
      JOIN public.server_members ON server_members.server_id = channels.server_id
      WHERE messages.id = message_reactions.message_id
      AND server_members.user_id = auth.uid()
    )
  );

CREATE POLICY "Allow users to remove their own reactions"
  ON public.message_reactions
  FOR DELETE
  USING (user_id = auth.uid());

-- Create RLS policies for direct_messages
CREATE POLICY "Allow users to view direct messages they sent or received"
  ON public.direct_messages
  FOR SELECT
  USING (sender_id = auth.uid() OR recipient_id = auth.uid());

CREATE POLICY "Allow users to send direct messages"
  ON public.direct_messages
  FOR INSERT
  WITH CHECK (sender_id = auth.uid());

CREATE POLICY "Allow users to update their own direct messages"
  ON public.direct_messages
  FOR UPDATE
  USING (sender_id = auth.uid());

CREATE POLICY "Allow users to delete their own direct messages"
  ON public.direct_messages
  FOR DELETE
  USING (sender_id = auth.uid());
