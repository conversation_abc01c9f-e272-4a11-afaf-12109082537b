import React from 'react';
import { useParams } from 'react-router-dom';
import Sidebar from '../components/Sidebar';
import ChannelSidebar from '../components/ChannelSidebar';
import ChatArea from '../components/ChatArea';
import MembersList from '../components/MembersList';

const ServerView: React.FC = () => {
  const { serverId, channelId } = useParams<{ serverId: string; channelId: string }>();

  return (
    <div className="flex h-screen bg-discord-bg">
      <Sidebar />
      <ChannelSidebar serverId={serverId} />
      <ChatArea channelId={channelId} />
      <MembersList serverId={serverId} />
    </div>
  );
};

export default ServerView;
