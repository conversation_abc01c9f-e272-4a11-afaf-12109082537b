Option Explicit

' Get the script directory
Dim fso, scriptDir
Set fso = CreateObject("Scripting.FileSystemObject")
scriptDir = fso.GetParentFolderName(WScript.ScriptFullName)

' Create Discord icon
CreateDiscordIcon

' Create shortcuts
CreateShortcuts

' Show success message
MsgBox "Discord Clone has been successfully set up!" & vbCrLf & vbCrLf & _
       "You can now launch Discord from:" & vbCrLf & _
       "1. The Desktop shortcut" & vbCrLf & _
       "2. The Start Menu shortcut" & vbCrLf & vbCrLf & _
       "Click OK to launch Discord now.", vbInformation, "Discord Setup Complete"

' Launch Discord
Dim shell
Set shell = CreateObject("WScript.Shell")
shell.Run """" & scriptDir & "\DiscordApp.hta" & """", 1, False

' Create a simple Discord icon
Sub CreateDiscordIcon()
    ' Create a simple icon file
    Dim iconContent
    iconContent = "<?xml version=""1.0"" encoding=""UTF-8"" standalone=""no""?>" & vbCrLf & _
                 "<svg xmlns=""http://www.w3.org/2000/svg"" width=""256"" height=""256"" viewBox=""0 0 256 256"">" & vbCrLf & _
                 "<rect width=""256"" height=""256"" fill=""#5865F2"" rx=""128"" ry=""128""/>" & vbCrLf & _
                 "<path fill=""white"" d=""M197.82,65.19c-13.26-6.12-27.51-10.66-42.39-13.26a.17.17,0,0,0-.18.09,164.87,164.87,0,0,0-7.31,15c-13.6-2-27.09-2-40.48,0a152.78,152.78,0,0,0-7.35-15,.18.18,0,0,0-.18-.09c-14.88,2.6-29.13,7.14-42.39,13.26a.16.16,0,0,0-.08.07C32.14,103.07,25.59,139.9,29,176.3a.18.18,0,0,0,.07.12,167.12,167.12,0,0,0,50.34,**********,0,0,0,.19-.06,119.48,119.48,0,0,0,10.3-***********,0,0,0-.1-.24A110.41,110.41,0,0,1,69,174.22a.18.18,0,0,1,0-.3,82.47,82.47,0,0,0,3.38-**********,0,0,1,.18,0,119.56,119.56,0,0,0,101.94,0,.17.17,0,0,1,.18,0,77.21,77.21,0,0,0,3.38,2.66.18.18,0,0,1,0,.3,103.35,103.35,0,0,1-20.86,10.63.18.18,0,0,0-.09.24,134.64,134.64,0,0,0,10.29,***********,0,0,0,.19.06,166.71,166.71,0,0,0,50.38-**********,0,0,0,.07-.12c4.06-42.14-6.8-78.61-28.81-111A.14.14,0,0,0,197.82,65.19ZM91.61,152.13c-9.91,0-18.13-9.1-18.13-20.27s8-20.27,18.13-20.27,18.31,9.27,18.13,20.27C109.74,143,101.69,152.13,91.61,152.13Zm66.83,0c-9.91,0-18.13-9.1-18.13-20.27s8-20.27,18.13-20.27,18.3,9.27,18.13,20.27C176.74,143,168.52,152.13,158.44,152.13Z""/>" & vbCrLf & _
                 "</svg>"

    ' Save the SVG file
    Dim svgFile
    Set svgFile = fso.CreateTextFile(scriptDir & "\discord_icon.svg", True)
    svgFile.Write iconContent
    svgFile.Close

    ' Create a simple ICO file (this is a placeholder - in a real scenario, you'd convert the SVG to ICO)
    ' For simplicity, we'll just create an empty ICO file
    Dim icoFile
    Set icoFile = fso.CreateTextFile(scriptDir & "\discord_icon.ico", True)
    icoFile.Close
End Sub

' Create desktop and start menu shortcuts
Sub CreateShortcuts()
    Dim shell, desktopPath, startMenuPath, shortcut
    
    Set shell = CreateObject("WScript.Shell")
    
    ' Create desktop shortcut
    desktopPath = shell.SpecialFolders("Desktop")
    Set shortcut = shell.CreateShortcut(desktopPath & "\Discord.lnk")
    shortcut.TargetPath = scriptDir & "\DiscordApp.hta"
    shortcut.WorkingDirectory = scriptDir
    shortcut.Description = "Discord Clone Application"
    shortcut.IconLocation = scriptDir & "\discord_icon.ico"
    shortcut.Save
    
    ' Create start menu shortcut
    startMenuPath = shell.SpecialFolders("StartMenu") & "\Programs"
    Set shortcut = shell.CreateShortcut(startMenuPath & "\Discord.lnk")
    shortcut.TargetPath = scriptDir & "\DiscordApp.hta"
    shortcut.WorkingDirectory = scriptDir
    shortcut.Description = "Discord Clone Application"
    shortcut.IconLocation = scriptDir & "\discord_icon.ico"
    shortcut.Save
End Sub
