import React, { useState, useEffect } from 'react';
import { Link, useParams } from 'react-router-dom';
import { getUserServers, createServer } from '../services/serverService';
import { Server } from '../types';
import Tooltip from './ui/Tooltip';
import ServerIcon from './ui/ServerIcon';
import CreateServerModal from './modals/CreateServerModal';

const ServerList: React.FC = () => {
  const [servers, setServers] = useState<Server[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const { serverId } = useParams<{ serverId: string }>();

  useEffect(() => {
    const fetchServers = async () => {
      try {
        const userServers = await getUserServers();
        setServers(userServers);
      } catch (err) {
        console.error('Error fetching servers:', err);
        setError('Failed to load servers');
      } finally {
        setLoading(false);
      }
    };

    fetchServers();
  }, []);

  const handleServerCreated = (newServer: Server) => {
    setServers(prev => [...prev, newServer]);
  };

  return (
    <div className="w-18 bg-discord-darkest flex flex-col items-center py-3 overflow-y-auto">
      {/* Home button */}
      <Tooltip content="Direct Messages">
        <Link to="/">
          <ServerIcon
            isHome={true}
            isActive={!serverId}
            onClick={() => {}}
          />
        </Link>
      </Tooltip>

      {/* Server list separator */}
      <div className="w-8 h-0.5 bg-discord-separator rounded-full my-2"></div>

      {/* Server list */}
      <div className="flex flex-col items-center space-y-2 w-full">
        {loading ? (
          <div className="w-12 h-12 rounded-full bg-discord-light flex items-center justify-center">
            <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-discord-primary"></div>
          </div>
        ) : (
          <>
            {servers.map(server => (
              <Tooltip key={server.id} content={server.name}>
                <Link to={`/servers/${server.id}`}>
                  <ServerIcon
                    server={server}
                    isActive={serverId === server.id}
                    hasUnread={false} // TODO: Implement unread logic
                    mentionCount={0} // TODO: Implement mention count
                    onClick={() => {}}
                  />
                </Link>
              </Tooltip>
            ))}

            {/* Add server button */}
            <Tooltip content="Add a Server">
              <ServerIcon
                isAddButton={true}
                onClick={() => setIsCreateModalOpen(true)}
              />
            </Tooltip>

            {/* Explore public servers button */}
            <Tooltip content="Explore Discoverable Servers">
              <ServerIcon
                isExploreButton={true}
                onClick={() => {
                  // TODO: Implement server discovery
                  console.log('Explore servers clicked');
                }}
              />
            </Tooltip>
          </>
        )}
      </div>

      {/* Create server modal */}
      <CreateServerModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onServerCreated={handleServerCreated}
      />
    </div>
  );
};

export default ServerList;
