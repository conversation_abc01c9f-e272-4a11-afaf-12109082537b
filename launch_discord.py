#!/usr/bin/env python3
"""
Discord Clone Launcher
A Python script to launch the Discord clone application
"""

import os
import sys
import webbrowser
import subprocess
import tkinter as tk
from tkinter import messagebox, ttk
from pathlib import Path

class DiscordLauncher:
    def __init__(self):
        self.script_dir = Path(__file__).parent.absolute()
        self.setup_gui()
    
    def setup_gui(self):
        """Create the launcher GUI"""
        self.root = tk.Tk()
        self.root.title("Discord Clone Launcher")
        self.root.geometry("400x300")
        self.root.configure(bg="#36393f")
        
        # Discord-like styling
        style = ttk.Style()
        style.theme_use('clam')
        
        # Title
        title_label = tk.Label(
            self.root,
            text="Discord Clone Launcher",
            font=("Segoe UI", 16, "bold"),
            fg="#ffffff",
            bg="#36393f"
        )
        title_label.pack(pady=20)
        
        # Description
        desc_label = tk.Label(
            self.root,
            text="Choose how you want to launch Discord Clone:",
            font=("Segoe UI", 10),
            fg="#dcddde",
            bg="#36393f"
        )
        desc_label.pack(pady=10)
        
        # Buttons frame
        buttons_frame = tk.Frame(self.root, bg="#36393f")
        buttons_frame.pack(pady=20)
        
        # Browser button
        browser_btn = tk.Button(
            buttons_frame,
            text="🌐 Open in Browser",
            font=("Segoe UI", 12),
            bg="#5865f2",
            fg="white",
            activebackground="#4752c4",
            activeforeground="white",
            relief="flat",
            padx=20,
            pady=10,
            command=self.launch_browser
        )
        browser_btn.pack(pady=5, fill="x")
        
        # Desktop app button
        desktop_btn = tk.Button(
            buttons_frame,
            text="🖥️ Desktop Application",
            font=("Segoe UI", 12),
            bg="#3ba55d",
            fg="white",
            activebackground="#2d7d32",
            activeforeground="white",
            relief="flat",
            padx=20,
            pady=10,
            command=self.launch_desktop
        )
        desktop_btn.pack(pady=5, fill="x")
        
        # React app button (if Node.js available)
        react_btn = tk.Button(
            buttons_frame,
            text="⚛️ React Application",
            font=("Segoe UI", 12),
            bg="#61dafb",
            fg="black",
            activebackground="#21a9c7",
            activeforeground="white",
            relief="flat",
            padx=20,
            pady=10,
            command=self.launch_react
        )
        react_btn.pack(pady=5, fill="x")
        
        # Status label
        self.status_label = tk.Label(
            self.root,
            text="Ready to launch",
            font=("Segoe UI", 9),
            fg="#72767d",
            bg="#36393f"
        )
        self.status_label.pack(side="bottom", pady=10)
        
        # Center the window
        self.center_window()
    
    def center_window(self):
        """Center the window on screen"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def update_status(self, message):
        """Update the status label"""
        self.status_label.config(text=message)
        self.root.update()
    
    def launch_browser(self):
        """Launch Discord clone in web browser"""
        try:
            self.update_status("Opening in browser...")
            
            # Check for index.html
            html_file = self.script_dir / "index.html"
            if html_file.exists():
                webbrowser.open(f"file://{html_file}")
                self.update_status("Opened in browser successfully!")
                messagebox.showinfo("Success", "Discord Clone opened in your default browser!")
            else:
                messagebox.showerror("Error", "index.html not found!")
                self.update_status("Error: index.html not found")
                
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open browser: {str(e)}")
            self.update_status("Error opening browser")
    
    def launch_desktop(self):
        """Launch Discord clone as desktop application"""
        try:
            self.update_status("Launching desktop app...")
            
            # Try HTA files first
            hta_files = ["DiscordApp.hta", "DiscordClone.hta"]
            
            for hta_file in hta_files:
                hta_path = self.script_dir / hta_file
                if hta_path.exists():
                    if sys.platform == "win32":
                        os.startfile(str(hta_path))
                        self.update_status("Desktop app launched!")
                        messagebox.showinfo("Success", f"Discord Clone launched as desktop app using {hta_file}!")
                        return
                    else:
                        messagebox.showwarning("Warning", "HTA files only work on Windows. Opening in browser instead.")
                        self.launch_browser()
                        return
            
            # Fallback to batch file
            bat_file = self.script_dir / "RunDiscord.bat"
            if bat_file.exists() and sys.platform == "win32":
                subprocess.Popen([str(bat_file)], shell=True)
                self.update_status("Launched via batch file!")
                messagebox.showinfo("Success", "Discord Clone launched!")
            else:
                messagebox.showerror("Error", "No desktop application files found!")
                self.update_status("Error: No desktop files found")
                
        except Exception as e:
            messagebox.showerror("Error", f"Failed to launch desktop app: {str(e)}")
            self.update_status("Error launching desktop app")
    
    def launch_react(self):
        """Launch React version of Discord clone"""
        try:
            self.update_status("Checking for Node.js...")
            
            # Check if package.json exists
            package_json = self.script_dir / "package.json"
            if not package_json.exists():
                messagebox.showerror("Error", "package.json not found! React version not available.")
                self.update_status("Error: React version not available")
                return
            
            # Check if Node.js is available
            try:
                result = subprocess.run(["node", "--version"], capture_output=True, text=True)
                if result.returncode != 0:
                    raise FileNotFoundError
                node_version = result.stdout.strip()
                self.update_status(f"Found Node.js {node_version}")
            except FileNotFoundError:
                messagebox.showerror(
                    "Node.js Required", 
                    "Node.js is not installed or not in PATH.\n\n"
                    "To use the React version:\n"
                    "1. Install Node.js from https://nodejs.org\n"
                    "2. Run 'npm install' in the project directory\n"
                    "3. Try again\n\n"
                    "For now, use the Browser or Desktop options."
                )
                self.update_status("Error: Node.js not found")
                return
            
            # Try to start the React app
            self.update_status("Starting React development server...")
            
            # Check if dependencies are installed
            node_modules = self.script_dir / "node_modules"
            if not node_modules.exists():
                response = messagebox.askyesno(
                    "Install Dependencies", 
                    "Dependencies not installed. Install them now?\n\n"
                    "This may take a few minutes..."
                )
                if response:
                    self.update_status("Installing dependencies...")
                    install_process = subprocess.Popen(
                        ["npm", "install"], 
                        cwd=str(self.script_dir),
                        shell=True
                    )
                    install_process.wait()
                    if install_process.returncode != 0:
                        messagebox.showerror("Error", "Failed to install dependencies!")
                        self.update_status("Error installing dependencies")
                        return
                else:
                    self.update_status("Cancelled")
                    return
            
            # Start the development server
            subprocess.Popen(
                ["npm", "start"], 
                cwd=str(self.script_dir),
                shell=True
            )
            
            self.update_status("React app starting...")
            messagebox.showinfo(
                "React App Starting", 
                "React development server is starting...\n\n"
                "The app will open in your browser automatically.\n"
                "If it doesn't open, go to http://localhost:3000"
            )
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to launch React app: {str(e)}")
            self.update_status("Error launching React app")
    
    def run(self):
        """Start the launcher"""
        self.root.mainloop()

def main():
    """Main function"""
    print("Discord Clone Launcher")
    print("=" * 30)
    
    # Check if we're in the right directory
    script_dir = Path(__file__).parent.absolute()
    
    # Quick launch options for command line
    if len(sys.argv) > 1:
        option = sys.argv[1].lower()
        launcher = DiscordLauncher()
        
        if option in ["browser", "web", "html"]:
            launcher.launch_browser()
        elif option in ["desktop", "hta", "app"]:
            launcher.launch_desktop()
        elif option in ["react", "dev", "npm"]:
            launcher.launch_react()
        else:
            print(f"Unknown option: {option}")
            print("Available options: browser, desktop, react")
        return
    
    # Launch GUI
    try:
        launcher = DiscordLauncher()
        launcher.run()
    except Exception as e:
        print(f"Error starting launcher: {e}")
        # Fallback to simple browser launch
        html_file = script_dir / "index.html"
        if html_file.exists():
            webbrowser.open(f"file://{html_file}")
            print("Opened Discord Clone in browser as fallback")
        else:
            print("Could not find Discord Clone files!")

if __name__ == "__main__":
    main()
