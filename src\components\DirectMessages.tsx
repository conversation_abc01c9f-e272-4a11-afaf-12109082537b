import React from 'react';
import UserPanel from './UserPanel';

// Mock data for direct messages
const friends = [
  { id: '1', name: '<PERSON>', status: 'online', avatar: '👨' },
  { id: '2', name: '<PERSON>', status: 'idle', avatar: '👩' },
  { id: '3', name: '<PERSON>', status: 'dnd', avatar: '👨‍🦰' },
  { id: '4', name: '<PERSON>', status: 'offline', avatar: '👩‍🦱' },
];

const DirectMessages: React.FC = () => {
  return (
    <div className="w-60 bg-discord-light-bg flex flex-col">
      {/* Search bar */}
      <div className="p-3">
        <div className="bg-discord-dark-bg rounded-md p-1 flex items-center">
          <input
            type="text"
            placeholder="Find or start a conversation"
            className="bg-transparent text-discord-muted text-sm w-full px-2 py-1 focus:outline-none"
          />
        </div>
      </div>

      {/* Navigation buttons */}
      <div className="px-2 mb-2">
        <button className="w-full text-left p-2 rounded flex items-center text-discord-channel hover:bg-discord-bg hover:text-discord-channel-selected">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5 mr-3">
            <path d="M11.47 3.84a.75.75 0 011.06 0l8.69 8.69a.75.75 0 101.06-1.06l-8.689-8.69a2.25 2.25 0 00-3.182 0l-8.69 8.69a.75.75 0 001.061 1.06l8.69-8.69z" />
            <path d="M12 5.432l8.159 8.159c.03.03.06.058.091.086v6.198c0 1.035-.84 1.875-1.875 1.875H15a.75.75 0 01-.75-.75v-4.5a.75.75 0 00-.75-.75h-3a.75.75 0 00-.75.75V21a.75.75 0 01-.75.75H5.625a1.875 1.875 0 01-1.875-1.875v-6.198a2.29 2.29 0 00.091-.086L12 5.43z" />
          </svg>
          Home
        </button>
        <button className="w-full text-left p-2 rounded flex items-center text-discord-channel hover:bg-discord-bg hover:text-discord-channel-selected">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5 mr-3">
            <path d="M4.913 2.658c2.075-.27 4.19-.408 6.337-.408 2.147 0 4.262.139 6.337.408 1.922.25 3.291 1.861 3.405 3.727a4.403 4.403 0 00-1.032-.211 50.89 50.89 0 00-8.42 0c-2.358.196-4.04 2.19-4.04 4.434v4.286a4.47 4.47 0 002.433 3.984L7.28 21.53A.75.75 0 016 21v-4.03a48.527 48.527 0 01-1.087-.128C2.905 16.58 1.5 14.833 1.5 12.862V6.638c0-1.97 1.405-3.718 3.413-3.979z" />
            <path d="M15.75 7.5c-1.376 0-2.739.057-4.086.169C10.124 7.797 9 9.103 9 10.609v4.285c0 1.507 1.128 2.814 2.67 2.94 1.243.102 2.5.157 3.768.165l2.782 2.781a.75.75 0 001.28-.53v-2.39l.33-.026c1.542-.125 2.67-1.433 2.67-2.94v-4.286c0-1.505-1.125-2.811-2.664-2.94A49.392 49.392 0 0015.75 7.5z" />
          </svg>
          Friends
        </button>
      </div>

      {/* Direct Messages header */}
      <div className="px-4 flex items-center justify-between text-xs font-semibold text-discord-muted mt-4 mb-1">
        <span>DIRECT MESSAGES</span>
        <button className="text-discord-muted hover:text-discord-text">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-4 h-4">
            <path fillRule="evenodd" d="M12 3.75a.75.75 0 01.75.75v6.75h6.75a.75.75 0 010 1.5h-6.75v6.75a.75.75 0 01-1.5 0v-6.75H4.5a.75.75 0 010-1.5h6.75V4.5a.75.75 0 01.75-.75z" clipRule="evenodd" />
          </svg>
        </button>
      </div>

      {/* Friends list */}
      <div className="px-2 overflow-y-auto flex-1">
        {friends.map((friend) => (
          <button
            key={friend.id}
            className="w-full text-left p-2 rounded flex items-center text-discord-channel hover:bg-discord-bg hover:text-discord-channel-selected"
          >
            <div className="relative mr-3">
              <div className="w-8 h-8 rounded-full bg-discord-dark-bg flex items-center justify-center">
                {friend.avatar}
              </div>
              <div className={`absolute bottom-0 right-0 w-3 h-3 rounded-full border-2 border-discord-light-bg ${
                friend.status === 'online' ? 'bg-discord-green' :
                friend.status === 'idle' ? 'bg-discord-yellow' :
                friend.status === 'dnd' ? 'bg-discord-red' : 'bg-discord-muted'
              }`}></div>
            </div>
            <span>{friend.name}</span>
          </button>
        ))}
      </div>

      {/* User panel */}
      <UserPanel />
    </div>
  );
};

export default DirectMessages;
