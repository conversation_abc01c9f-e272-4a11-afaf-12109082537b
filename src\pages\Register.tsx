import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { AuthError } from '@supabase/supabase-js';

const Register: React.FC = () => {
  const [email, setEmail] = useState('');
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();
  const { register } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setSuccess('');
    setIsLoading(true);

    // Basic validation
    if (password.length < 6) {
      setError('Password must be at least 6 characters long');
      setIsLoading(false);
      return;
    }

    try {
      await register(email, username, password);
      setError('');
      // Show success message instead of immediately navigating
      // This is because Supabase might require email confirmation
      setSuccess('Registration successful! Please check your email to confirm your account.');
      setIsLoading(false);
    } catch (err) {
      const authError = err as AuthError;

      // Handle specific Supabase auth errors
      switch (authError.message) {
        case 'User already registered':
          setError('An account with this email already exists.');
          break;
        case 'Password should be at least 6 characters':
          setError('Password should be at least 6 characters.');
          break;
        case 'Rate limit exceeded':
          setError('Too many registration attempts. Please try again later.');
          break;
        default:
          setError(authError.message || 'Failed to create an account. Please try again.');
      }

      console.error(err);
      setIsLoading(false);
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-discord-dark-bg">
      <div className="w-full max-w-md p-8 space-y-8 bg-discord-light-bg rounded-lg shadow-lg">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-white">Create an account</h1>
        </div>

        {error && (
          <div className="p-4 text-white bg-discord-red rounded-md">
            {error}
          </div>
        )}

        {success && (
          <div className="p-4 text-white bg-discord-green rounded-md">
            {success}
          </div>
        )}

        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-discord-channel">
              EMAIL
            </label>
            <input
              id="email"
              name="email"
              type="email"
              required
              className="w-full px-3 py-2 mt-1 text-white bg-discord-dark-bg border border-discord-separator rounded-md focus:outline-none focus:ring-2 focus:ring-discord-primary"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
            />
          </div>

          <div>
            <label htmlFor="username" className="block text-sm font-medium text-discord-channel">
              USERNAME
            </label>
            <input
              id="username"
              name="username"
              type="text"
              required
              className="w-full px-3 py-2 mt-1 text-white bg-discord-dark-bg border border-discord-separator rounded-md focus:outline-none focus:ring-2 focus:ring-discord-primary"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
            />
          </div>

          <div>
            <label htmlFor="password" className="block text-sm font-medium text-discord-channel">
              PASSWORD
            </label>
            <input
              id="password"
              name="password"
              type="password"
              required
              className="w-full px-3 py-2 mt-1 text-white bg-discord-dark-bg border border-discord-separator rounded-md focus:outline-none focus:ring-2 focus:ring-discord-primary"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
            />
          </div>

          <div>
            <button
              type="submit"
              disabled={isLoading}
              className="w-full px-4 py-2 text-white bg-discord-primary rounded-md hover:bg-opacity-80 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-discord-primary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <span className="flex items-center justify-center">
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Creating account...
                </span>
              ) : 'Continue'}
            </button>
          </div>

          <div className="text-sm text-discord-muted">
            Already have an account?{' '}
            <Link to="/login" className="text-discord-link hover:underline">
              Login
            </Link>
          </div>
        </form>
      </div>
    </div>
  );
};

export default Register;
