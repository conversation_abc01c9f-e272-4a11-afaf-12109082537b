/**
 * Parse Discord-style markdown to HTML
 * @param text Text with Discord markdown
 * @returns HTML string with formatted text
 */
export const parseMarkdown = (text: string): string => {
  if (!text) return '';
  
  // Replace newlines with <br>
  let formattedText = text.replace(/\\n/g, '<br>');
  
  // Bold: **text** or __text__
  formattedText = formattedText.replace(/(\*\*|__)(.*?)\1/g, '<strong>$2</strong>');
  
  // Italic: *text* or _text_
  formattedText = formattedText.replace(/(\*|_)(.*?)\1/g, '<em>$2</em>');
  
  // Strikethrough: ~~text~~
  formattedText = formattedText.replace(/~~(.*?)~~/g, '<del>$1</del>');
  
  // Underline: __text__
  formattedText = formattedText.replace(/__([^_]+)__/g, '<u>$1</u>');
  
  // Code block: ```language\ncode```
  formattedText = formattedText.replace(/```(\w+)?\n([\s\S]*?)```/g, '<pre><code class="language-$1">$2</code></pre>');
  
  // Inline code: `code`
  formattedText = formattedText.replace(/`([^`]+)`/g, '<code>$1</code>');
  
  // Quote: > text
  formattedText = formattedText.replace(/^> (.+)$/gm, '<blockquote>$1</blockquote>');
  
  // Spoiler: ||text||
  formattedText = formattedText.replace(/\|\|(.*?)\|\|/g, '<span class="spoiler">$1</span>');
  
  // URLs
  const urlRegex = /(https?:\/\/[^\s<]+[^<.,:;"')\]\s])/g;
  formattedText = formattedText.replace(urlRegex, '<a href="$1" target="_blank" rel="noopener noreferrer">$1</a>');
  
  return formattedText;
};

/**
 * Parse mentions in a message
 * @param text Message text
 * @param users Map of user IDs to usernames
 * @param channels Map of channel IDs to channel names
 * @param roles Map of role IDs to role names
 * @returns Text with formatted mentions
 */
export const parseMentions = (
  text: string,
  users: Record<string, string> = {},
  channels: Record<string, string> = {},
  roles: Record<string, string> = {}
): string => {
  if (!text) return '';
  
  // User mentions: <@123456789>
  let parsedText = text.replace(/<@!?(\d+)>/g, (match, userId) => {
    const username = users[userId] || 'Unknown User';
    return `<span class="mention user">@${username}</span>`;
  });
  
  // Channel mentions: <#123456789>
  parsedText = parsedText.replace(/<#(\d+)>/g, (match, channelId) => {
    const channelName = channels[channelId] || 'unknown-channel';
    return `<span class="mention channel">#${channelName}</span>`;
  });
  
  // Role mentions: <@&123456789>
  parsedText = parsedText.replace(/<@&(\d+)>/g, (match, roleId) => {
    const roleName = roles[roleId] || 'Unknown Role';
    return `<span class="mention role">@${roleName}</span>`;
  });
  
  // @everyone and @here mentions
  parsedText = parsedText.replace(/@(everyone|here)/g, '<span class="mention everyone">@$1</span>');
  
  return parsedText;
};

/**
 * Parse emojis in a message
 * @param text Message text
 * @returns Text with formatted emojis
 */
export const parseEmojis = (text: string): string => {
  if (!text) return '';
  
  // Custom emoji: <:name:id>
  return text.replace(/<:([a-zA-Z0-9_]+):(\d+)>/g, (match, name, id) => {
    return `<img class="emoji" alt=":${name}:" src="https://cdn.discordapp.com/emojis/${id}.png" />`;
  });
};
