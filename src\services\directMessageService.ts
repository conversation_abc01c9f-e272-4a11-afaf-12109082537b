import { supabase } from '../lib/supabase';
import { DirectMessage, User } from '../types';

/**
 * Get direct messages between the current user and another user
 */
export const getDirectMessages = async (
  otherUserId: string,
  limit = 50,
  before?: string
): Promise<DirectMessage[]> => {
  const { data: userData } = await supabase.auth.getUser();
  
  if (!userData.user) {
    console.error('User not authenticated');
    return [];
  }

  let query = supabase
    .from('direct_messages')
    .select(`
      id,
      sender_id,
      recipient_id,
      content,
      created_at,
      updated_at,
      edited,
      read,
      sender:sender_id (
        id,
        username,
        discriminator,
        avatar,
        status
      ),
      recipient:recipient_id (
        id,
        username,
        discriminator,
        avatar,
        status
      )
    `)
    .or(`sender_id.eq.${userData.user.id},recipient_id.eq.${userData.user.id}`)
    .or(`sender_id.eq.${otherUserId},recipient_id.eq.${otherUserId}`)
    .order('created_at', { ascending: false })
    .limit(limit);

  if (before) {
    // Get messages before a certain message ID
    const { data: beforeMessage } = await supabase
      .from('direct_messages')
      .select('created_at')
      .eq('id', before)
      .single();

    if (beforeMessage) {
      query = query.lt('created_at', beforeMessage.created_at);
    }
  }

  const { data, error } = await query;

  if (error) {
    console.error('Error fetching direct messages:', error);
    return [];
  }

  // Transform the data to match our DirectMessage type
  const messages = data
    .filter(message => 
      (message.sender_id === userData.user.id && message.recipient_id === otherUserId) ||
      (message.sender_id === otherUserId && message.recipient_id === userData.user.id)
    )
    .map(message => ({
      id: message.id,
      sender: {
        id: message.sender.id,
        username: message.sender.username,
        discriminator: message.sender.discriminator,
        avatar: message.sender.avatar,
        status: message.sender.status
      },
      recipient: {
        id: message.recipient.id,
        username: message.recipient.username,
        discriminator: message.recipient.discriminator,
        avatar: message.recipient.avatar,
        status: message.recipient.status
      },
      content: message.content,
      timestamp: message.created_at,
      editedTimestamp: message.edited ? message.updated_at : undefined,
      read: message.read
    }));

  return messages.reverse(); // Reverse to get oldest first
};

/**
 * Send a direct message to another user
 */
export const sendDirectMessage = async (
  recipientId: string,
  content: string
): Promise<DirectMessage | null> => {
  const { data: userData } = await supabase.auth.getUser();
  
  if (!userData.user) {
    console.error('User not authenticated');
    return null;
  }

  const { data, error } = await supabase
    .from('direct_messages')
    .insert({
      sender_id: userData.user.id,
      recipient_id: recipientId,
      content
    })
    .select(`
      id,
      sender_id,
      recipient_id,
      content,
      created_at,
      updated_at,
      edited,
      read,
      sender:sender_id (
        id,
        username,
        discriminator,
        avatar,
        status
      ),
      recipient:recipient_id (
        id,
        username,
        discriminator,
        avatar,
        status
      )
    `)
    .single();

  if (error) {
    console.error('Error sending direct message:', error);
    return null;
  }

  return {
    id: data.id,
    sender: {
      id: data.sender.id,
      username: data.sender.username,
      discriminator: data.sender.discriminator,
      avatar: data.sender.avatar,
      status: data.sender.status
    },
    recipient: {
      id: data.recipient.id,
      username: data.recipient.username,
      discriminator: data.recipient.discriminator,
      avatar: data.recipient.avatar,
      status: data.recipient.status
    },
    content: data.content,
    timestamp: data.created_at,
    editedTimestamp: data.edited ? data.updated_at : undefined,
    read: data.read
  };
};

/**
 * Mark direct messages as read
 */
export const markDirectMessagesAsRead = async (senderId: string): Promise<boolean> => {
  const { data: userData } = await supabase.auth.getUser();
  
  if (!userData.user) {
    console.error('User not authenticated');
    return false;
  }

  const { error } = await supabase
    .from('direct_messages')
    .update({ read: true })
    .eq('sender_id', senderId)
    .eq('recipient_id', userData.user.id)
    .eq('read', false);

  if (error) {
    console.error('Error marking messages as read:', error);
    return false;
  }

  return true;
};

/**
 * Get all users the current user has direct messages with
 */
export const getDirectMessageUsers = async (): Promise<User[]> => {
  const { data: userData } = await supabase.auth.getUser();
  
  if (!userData.user) {
    console.error('User not authenticated');
    return [];
  }

  // Get all users the current user has sent messages to
  const { data: sentTo, error: sentError } = await supabase
    .from('direct_messages')
    .select('recipient_id')
    .eq('sender_id', userData.user.id)
    .distinct();

  if (sentError) {
    console.error('Error fetching sent messages:', sentError);
    return [];
  }

  // Get all users the current user has received messages from
  const { data: receivedFrom, error: receivedError } = await supabase
    .from('direct_messages')
    .select('sender_id')
    .eq('recipient_id', userData.user.id)
    .distinct();

  if (receivedError) {
    console.error('Error fetching received messages:', receivedError);
    return [];
  }

  // Combine and deduplicate user IDs
  const userIds = [...new Set([
    ...sentTo.map(item => item.recipient_id),
    ...receivedFrom.map(item => item.sender_id)
  ])];

  if (userIds.length === 0) {
    return [];
  }

  // Get user profiles
  const { data: profiles, error: profilesError } = await supabase
    .from('profiles')
    .select('id, username, discriminator, avatar, status')
    .in('id', userIds);

  if (profilesError) {
    console.error('Error fetching user profiles:', profilesError);
    return [];
  }

  return profiles.map(profile => ({
    id: profile.id,
    username: profile.username,
    discriminator: profile.discriminator,
    avatar: profile.avatar,
    status: profile.status
  }));
};

/**
 * Subscribe to new direct messages
 */
export const subscribeToDirectMessages = (
  callback: (message: DirectMessage) => void
) => {
  const { data: userData } = supabase.auth.getUser();
  
  if (!userData.user) {
    console.error('User not authenticated');
    return () => {};
  }

  const subscription = supabase
    .channel(`direct-messages-${userData.user.id}`)
    .on(
      'postgres_changes',
      {
        event: 'INSERT',
        schema: 'public',
        table: 'direct_messages',
        filter: `recipient_id=eq.${userData.user.id}`
      },
      async (payload) => {
        // Fetch the complete message with sender and recipient info
        const { data, error } = await supabase
          .from('direct_messages')
          .select(`
            id,
            sender_id,
            recipient_id,
            content,
            created_at,
            updated_at,
            edited,
            read,
            sender:sender_id (
              id,
              username,
              discriminator,
              avatar,
              status
            ),
            recipient:recipient_id (
              id,
              username,
              discriminator,
              avatar,
              status
            )
          `)
          .eq('id', payload.new.id)
          .single();

        if (error || !data) {
          console.error('Error fetching new direct message:', error);
          return;
        }

        const message: DirectMessage = {
          id: data.id,
          sender: {
            id: data.sender.id,
            username: data.sender.username,
            discriminator: data.sender.discriminator,
            avatar: data.sender.avatar,
            status: data.sender.status
          },
          recipient: {
            id: data.recipient.id,
            username: data.recipient.username,
            discriminator: data.recipient.discriminator,
            avatar: data.recipient.avatar,
            status: data.recipient.status
          },
          content: data.content,
          timestamp: data.created_at,
          editedTimestamp: data.edited ? data.updated_at : undefined,
          read: data.read
        };

        callback(message);
      }
    )
    .subscribe();

  return () => {
    supabase.removeChannel(subscription);
  };
};
