import React, { useState } from 'react';
import { Message } from '../../types';
import Tooltip from './Tooltip';
import StatusIndicator from './StatusIndicator';

interface MessageComponentProps {
  message: Message;
  isGrouped?: boolean;
  onReply?: (message: Message) => void;
  onEdit?: (message: Message) => void;
  onDelete?: (message: Message) => void;
  onReact?: (message: Message, emoji: string) => void;
  currentUserId?: string;
}

const MessageComponent: React.FC<MessageComponentProps> = ({
  message,
  isGrouped = false,
  onReply,
  onEdit,
  onDelete,
  onReact,
  currentUserId
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [showActions, setShowActions] = useState(false);

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const isToday = date.toDateString() === now.toDateString();
    
    if (isToday) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else {
      return date.toLocaleDateString([], { 
        month: '2-digit', 
        day: '2-digit', 
        year: 'numeric' 
      }) + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }
  };

  const isOwnMessage = currentUserId === message.author.id;

  return (
    <div
      className={`group relative px-4 py-0.5 hover:bg-discord-hover-bg transition-colors ${
        isGrouped ? 'mt-0.5' : 'mt-4'
      }`}
      onMouseEnter={() => {
        setIsHovered(true);
        setShowActions(true);
      }}
      onMouseLeave={() => {
        setIsHovered(false);
        setShowActions(false);
      }}
    >
      <div className="flex">
        {/* Avatar (only for non-grouped messages) */}
        {!isGrouped && (
          <div className="relative mr-4 mt-0.5">
            <div className="w-10 h-10 rounded-full bg-discord-primary flex items-center justify-center text-white font-medium">
              {message.author.avatar}
            </div>
            <div className="absolute -bottom-0.5 -right-0.5">
              <StatusIndicator 
                status={message.author.status} 
                size="sm"
                className="border-2 border-discord-bg"
              />
            </div>
          </div>
        )}

        {/* Grouped message spacing */}
        {isGrouped && <div className="w-14 flex-shrink-0" />}

        {/* Message content */}
        <div className="flex-1 min-w-0">
          {/* Header (only for non-grouped messages) */}
          {!isGrouped && (
            <div className="flex items-baseline mb-1">
              <span className="font-medium text-white mr-2">
                {message.author.username}
              </span>
              {message.author.isBot && (
                <span className="bg-discord-primary text-white text-xs px-1 py-0.5 rounded mr-2 font-medium">
                  BOT
                </span>
              )}
              <span className="text-xs text-discord-text-muted">
                {formatTimestamp(message.timestamp)}
              </span>
            </div>
          )}

          {/* Grouped message timestamp (visible on hover) */}
          {isGrouped && isHovered && (
            <div className="absolute left-0 top-0 text-xs text-discord-text-muted opacity-50 w-14 text-right pr-2">
              {new Date(message.timestamp).toLocaleTimeString([], { 
                hour: '2-digit', 
                minute: '2-digit' 
              })}
            </div>
          )}

          {/* Message text */}
          <div className="text-discord-text-normal leading-relaxed">
            {message.content}
            {message.editedTimestamp && (
              <Tooltip content={`Edited ${formatTimestamp(message.editedTimestamp)}`}>
                <span className="text-xs text-discord-text-muted ml-1 cursor-help">
                  (edited)
                </span>
              </Tooltip>
            )}
          </div>

          {/* Attachments */}
          {message.attachments && message.attachments.length > 0 && (
            <div className="mt-2 space-y-2">
              {message.attachments.map((attachment) => (
                <div
                  key={attachment.id}
                  className="bg-discord-darker border border-discord-separator rounded p-3 max-w-md"
                >
                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-discord-primary rounded flex items-center justify-center mr-3">
                      <svg width="16" height="16" viewBox="0 0 24 24" className="text-white">
                        <path
                          fill="currentColor"
                          d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"
                        />
                      </svg>
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="text-discord-text-link font-medium truncate">
                        {attachment.filename}
                      </div>
                      <div className="text-xs text-discord-text-muted">
                        {(attachment.size / 1024).toFixed(1)} KB
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Reactions */}
          {message.reactions && message.reactions.length > 0 && (
            <div className="flex flex-wrap gap-1 mt-2">
              {message.reactions.map((reaction, index) => (
                <button
                  key={index}
                  onClick={() => onReact?.(message, reaction.emoji.name)}
                  className={`flex items-center px-2 py-1 rounded border transition-colors ${
                    reaction.me
                      ? 'bg-discord-primary bg-opacity-20 border-discord-primary text-discord-primary'
                      : 'bg-discord-darker border-discord-separator text-discord-text-muted hover:border-discord-interactive-normal'
                  }`}
                >
                  <span className="mr-1">{reaction.emoji.name}</span>
                  <span className="text-xs">{reaction.count}</span>
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Message actions (visible on hover) */}
        {showActions && (
          <div className="absolute top-0 right-4 bg-discord-bg border border-discord-separator rounded shadow-lg flex">
            <Tooltip content="Add Reaction">
              <button
                onClick={() => {
                  // TODO: Open emoji picker for reactions
                  onReact?.(message, '👍');
                }}
                className="w-8 h-8 flex items-center justify-center text-discord-interactive-normal hover:text-discord-interactive-hover hover:bg-discord-hover-bg transition-colors"
              >
                <svg width="16" height="16" viewBox="0 0 24 24">
                  <path
                    fill="currentColor"
                    d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12C4 7.59 7.59 4 12 4C16.41 4 20 7.59 20 12C20 16.41 16.41 20 12 20ZM8.5 11C9.33 11 10 10.33 10 9.5C10 8.67 9.33 8 8.5 8C7.67 8 7 8.67 7 9.5C7 10.33 7.67 11 8.5 11ZM15.5 11C16.33 11 17 10.33 17 9.5C17 8.67 16.33 8 15.5 8C14.67 8 14 8.67 14 9.5C14 10.33 14.67 11 15.5 11ZM12 17.5C14.33 17.5 16.31 16.04 17 14H7C7.69 16.04 9.67 17.5 12 17.5Z"
                  />
                </svg>
              </button>
            </Tooltip>

            <Tooltip content="Reply">
              <button
                onClick={() => onReply?.(message)}
                className="w-8 h-8 flex items-center justify-center text-discord-interactive-normal hover:text-discord-interactive-hover hover:bg-discord-hover-bg transition-colors"
              >
                <svg width="16" height="16" viewBox="0 0 24 24">
                  <path
                    fill="currentColor"
                    d="M10 9V5L3 12L10 19V14.9C15 14.9 18.5 16.5 21 20C20 15 17 10 10 9Z"
                  />
                </svg>
              </button>
            </Tooltip>

            {isOwnMessage && (
              <>
                <Tooltip content="Edit">
                  <button
                    onClick={() => onEdit?.(message)}
                    className="w-8 h-8 flex items-center justify-center text-discord-interactive-normal hover:text-discord-interactive-hover hover:bg-discord-hover-bg transition-colors"
                  >
                    <svg width="16" height="16" viewBox="0 0 24 24">
                      <path
                        fill="currentColor"
                        d="M3 17.25V21H6.75L17.81 9.94L14.06 6.19L3 17.25ZM20.71 7.04C21.1 6.65 21.1 6.02 20.71 5.63L18.37 3.29C17.98 2.9 17.35 2.9 16.96 3.29L15.13 5.12L18.88 8.87L20.71 7.04Z"
                      />
                    </svg>
                  </button>
                </Tooltip>

                <Tooltip content="Delete">
                  <button
                    onClick={() => onDelete?.(message)}
                    className="w-8 h-8 flex items-center justify-center text-discord-interactive-normal hover:text-discord-red hover:bg-discord-hover-bg transition-colors"
                  >
                    <svg width="16" height="16" viewBox="0 0 24 24">
                      <path
                        fill="currentColor"
                        d="M6 19C6 20.1 6.9 21 8 21H16C17.1 21 18 20.1 18 19V7H6V19ZM19 4H15.5L14.5 3H9.5L8.5 4H5V6H19V4Z"
                      />
                    </svg>
                  </button>
                </Tooltip>
              </>
            )}

            <Tooltip content="More">
              <button className="w-8 h-8 flex items-center justify-center text-discord-interactive-normal hover:text-discord-interactive-hover hover:bg-discord-hover-bg transition-colors">
                <svg width="16" height="16" viewBox="0 0 24 24">
                  <path
                    fill="currentColor"
                    d="M12 8C13.1 8 14 7.1 14 6C14 4.9 13.1 4 12 4C10.9 4 10 4.9 10 6C10 7.1 10.9 8 12 8ZM12 10C10.9 10 10 10.9 10 12C10 13.1 10.9 14 12 14C13.1 14 14 13.1 14 12C14 10.9 13.1 10 12 10ZM12 16C10.9 16 10 16.9 10 18C10 19.1 10.9 20 12 20C13.1 20 14 19.1 14 18C14 16.9 13.1 16 12 16Z"
                  />
                </svg>
              </button>
            </Tooltip>
          </div>
        )}
      </div>
    </div>
  );
};

export default MessageComponent;
