{"name": "discord-clone", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.11", "@types/react": "^18.0.27", "@types/react-dom": "^18.0.10", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "react-scripts": "5.0.1", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "tailwindcss": "^3.2.4", "postcss": "^8.4.21", "autoprefixer": "^10.4.13", "@supabase/supabase-js": "^2.39.0", "zustand": "^4.3.2"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "electron-dev": "concurrently \"cross-env BROWSER=none npm start\" \"wait-on http://localhost:3000 && electron .\"", "electron-pack": "electron-builder -c.extraMetadata.main=electron.js", "preelectron-pack": "npm run build", "desktop": "electron ."}, "main": "electron.js", "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "build": {"appId": "com.discord.clone", "productName": "<PERSON><PERSON>", "files": ["build/**/*", "electron.js", "node_modules/**/*"], "directories": {"buildResources": "public"}, "win": {"target": "nsis", "icon": "public/favicon.ico"}, "mac": {"target": "dmg", "icon": "public/logo512.png"}, "linux": {"target": "deb", "icon": "public/logo512.png"}}}