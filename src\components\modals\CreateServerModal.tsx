import React, { useState } from 'react';
import { createServer } from '../../services/serverService';
import { Server } from '../../types';

interface CreateServerModalProps {
  isOpen: boolean;
  onClose: () => void;
  onServerCreated: (server: Server) => void;
}

const CreateServerModal: React.FC<CreateServerModalProps> = ({
  isOpen,
  onClose,
  onServerCreated
}) => {
  const [step, setStep] = useState<'template' | 'customize'>('template');
  const [serverName, setServerName] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const handleCreateServer = async () => {
    if (!serverName.trim()) {
      setError('Server name is required');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      const newServer = await createServer(serverName);
      if (newServer) {
        onServerCreated(newServer);
        onClose();
        setStep('template');
        setServerName('');
      } else {
        setError('Failed to create server');
      }
    } catch (err) {
      setError('An error occurred while creating the server');
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    onClose();
    setStep('template');
    setServerName('');
    setError('');
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg w-full max-w-md mx-4 overflow-hidden">
        {step === 'template' && (
          <div className="p-6">
            <div className="text-center mb-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                Create a server
              </h2>
              <p className="text-gray-600">
                Your server is where you and your friends hang out. Make yours and start talking.
              </p>
            </div>

            <div className="space-y-3">
              <button
                onClick={() => setStep('customize')}
                className="w-full p-4 border-2 border-gray-200 rounded-lg hover:border-discord-primary transition-colors group"
              >
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-discord-primary rounded-lg flex items-center justify-center mr-4">
                    <svg width="24" height="24" viewBox="0 0 24 24" className="text-white">
                      <path fill="currentColor" d="M20 11.1111H12.8889V4H11.1111V11.1111H4V12.8889H11.1111V20H12.8889V12.8889H20V11.1111Z"/>
                    </svg>
                  </div>
                  <div className="text-left">
                    <h3 className="font-semibold text-gray-900 group-hover:text-discord-primary">
                      Create My Own
                    </h3>
                    <p className="text-sm text-gray-600">
                      Start from scratch
                    </p>
                  </div>
                </div>
              </button>

              <div className="text-center py-2">
                <span className="text-sm font-semibold text-gray-500 uppercase tracking-wide">
                  Start from a template
                </span>
              </div>

              <button className="w-full p-4 border-2 border-gray-200 rounded-lg hover:border-discord-primary transition-colors group">
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mr-4">
                    <span className="text-2xl">🎮</span>
                  </div>
                  <div className="text-left">
                    <h3 className="font-semibold text-gray-900 group-hover:text-discord-primary">
                      Gaming
                    </h3>
                    <p className="text-sm text-gray-600">
                      Hang out and have fun
                    </p>
                  </div>
                </div>
              </button>

              <button className="w-full p-4 border-2 border-gray-200 rounded-lg hover:border-discord-primary transition-colors group">
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mr-4">
                    <span className="text-2xl">🎓</span>
                  </div>
                  <div className="text-left">
                    <h3 className="font-semibold text-gray-900 group-hover:text-discord-primary">
                      School Club
                    </h3>
                    <p className="text-sm text-gray-600">
                      For school clubs and classes
                    </p>
                  </div>
                </div>
              </button>

              <button className="w-full p-4 border-2 border-gray-200 rounded-lg hover:border-discord-primary transition-colors group">
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mr-4">
                    <span className="text-2xl">📚</span>
                  </div>
                  <div className="text-left">
                    <h3 className="font-semibold text-gray-900 group-hover:text-discord-primary">
                      Study Group
                    </h3>
                    <p className="text-sm text-gray-600">
                      Get help with homework
                    </p>
                  </div>
                </div>
              </button>
            </div>

            <div className="mt-6 flex justify-between">
              <button
                onClick={handleClose}
                className="px-4 py-2 text-gray-600 hover:text-gray-800"
              >
                Back
              </button>
            </div>
          </div>
        )}

        {step === 'customize' && (
          <div className="p-6">
            <div className="text-center mb-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                Customize your server
              </h2>
              <p className="text-gray-600">
                Give your new server a personality with a name and an icon. You can always change it later.
              </p>
            </div>

            <div className="mb-6">
              <div className="flex justify-center mb-4">
                <div className="w-20 h-20 bg-discord-light rounded-full flex items-center justify-center border-4 border-dashed border-gray-300">
                  <svg width="24" height="24" viewBox="0 0 24 24" className="text-gray-400">
                    <path fill="currentColor" d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                  </svg>
                </div>
              </div>
              <p className="text-center text-sm text-gray-500">
                Upload an image
              </p>
            </div>

            {error && (
              <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
                {error}
              </div>
            )}

            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                SERVER NAME
              </label>
              <input
                type="text"
                value={serverName}
                onChange={(e) => setServerName(e.target.value)}
                placeholder="Enter server name"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-discord-primary focus:border-transparent"
                maxLength={100}
              />
              <p className="text-xs text-gray-500 mt-1">
                By creating a server, you agree to Discord's Community Guidelines.
              </p>
            </div>

            <div className="flex justify-between">
              <button
                onClick={() => setStep('template')}
                className="px-4 py-2 text-gray-600 hover:text-gray-800"
                disabled={isLoading}
              >
                Back
              </button>
              <button
                onClick={handleCreateServer}
                disabled={!serverName.trim() || isLoading}
                className="px-6 py-2 bg-discord-primary text-white rounded-md hover:bg-opacity-90 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? 'Creating...' : 'Create'}
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CreateServerModal;
