import { supabase } from '../lib/supabase';
import { Message } from '../types';

/**
 * Get messages for a channel
 */
export const getChannelMessages = async (
  channelId: string,
  limit = 50,
  before?: string
): Promise<Message[]> => {
  let query = supabase
    .from('messages')
    .select(`
      id,
      channel_id,
      user_id,
      content,
      created_at,
      updated_at,
      edited,
      reply_to_id,
      profiles:user_id (
        id,
        username,
        discriminator,
        avatar,
        status
      ),
      message_attachments (
        id,
        file_name,
        file_size,
        file_type,
        url
      ),
      message_reactions (
        id,
        emoji,
        user_id
      )
    `)
    .eq('channel_id', channelId)
    .order('created_at', { ascending: false })
    .limit(limit);

  if (before) {
    // Get messages before a certain message ID
    const { data: beforeMessage } = await supabase
      .from('messages')
      .select('created_at')
      .eq('id', before)
      .single();

    if (beforeMessage) {
      query = query.lt('created_at', beforeMessage.created_at);
    }
  }

  const { data, error } = await query;

  if (error) {
    console.error('Error fetching messages:', error);
    return [];
  }

  // Transform the data to match our Message type
  const messages = data.map(message => ({
    id: message.id,
    channelId: message.channel_id,
    author: {
      id: message.profiles.id,
      username: message.profiles.username,
      avatar: message.profiles.avatar,
      discriminator: message.profiles.discriminator,
      status: message.profiles.status,
      isBot: false
    },
    content: message.content,
    timestamp: message.created_at,
    editedTimestamp: message.edited ? message.updated_at : undefined,
    attachments: message.message_attachments.map(attachment => ({
      id: attachment.id,
      filename: attachment.file_name,
      size: attachment.file_size,
      url: attachment.url,
      contentType: attachment.file_type
    })),
    reactions: message.message_reactions.reduce((acc: any[], reaction) => {
      const existingReaction = acc.find(r => r.emoji.name === reaction.emoji);
      if (existingReaction) {
        existingReaction.count += 1;
        if (reaction.user_id === message.user_id) {
          existingReaction.me = true;
        }
      } else {
        acc.push({
          emoji: {
            name: reaction.emoji
          },
          count: 1,
          me: reaction.user_id === message.user_id
        });
      }
      return acc;
    }, []),
    mentions: [],
    referencedMessage: message.reply_to_id ? undefined : undefined // We would need to fetch this separately
  }));

  return messages.reverse(); // Reverse to get oldest first
};

/**
 * Send a message to a channel
 */
export const sendMessage = async (
  channelId: string,
  content: string,
  replyToId?: string
): Promise<Message | null> => {
  const { data: userData } = await supabase.auth.getUser();
  
  if (!userData.user) {
    console.error('User not authenticated');
    return null;
  }

  const { data, error } = await supabase
    .from('messages')
    .insert({
      channel_id: channelId,
      user_id: userData.user.id,
      content,
      reply_to_id: replyToId
    })
    .select(`
      id,
      channel_id,
      user_id,
      content,
      created_at,
      updated_at,
      edited,
      reply_to_id,
      profiles:user_id (
        id,
        username,
        discriminator,
        avatar,
        status
      )
    `)
    .single();

  if (error) {
    console.error('Error sending message:', error);
    return null;
  }

  return {
    id: data.id,
    channelId: data.channel_id,
    author: {
      id: data.profiles.id,
      username: data.profiles.username,
      avatar: data.profiles.avatar,
      discriminator: data.profiles.discriminator,
      status: data.profiles.status,
      isBot: false
    },
    content: data.content,
    timestamp: data.created_at,
    editedTimestamp: data.edited ? data.updated_at : undefined,
    attachments: [],
    reactions: [],
    mentions: [],
    referencedMessage: data.reply_to_id ? undefined : undefined
  };
};

/**
 * Update a message
 */
export const updateMessage = async (
  messageId: string,
  content: string
): Promise<Message | null> => {
  const { data, error } = await supabase
    .from('messages')
    .update({
      content,
      edited: true
    })
    .eq('id', messageId)
    .select(`
      id,
      channel_id,
      user_id,
      content,
      created_at,
      updated_at,
      edited,
      reply_to_id,
      profiles:user_id (
        id,
        username,
        discriminator,
        avatar,
        status
      )
    `)
    .single();

  if (error) {
    console.error('Error updating message:', error);
    return null;
  }

  return {
    id: data.id,
    channelId: data.channel_id,
    author: {
      id: data.profiles.id,
      username: data.profiles.username,
      avatar: data.profiles.avatar,
      discriminator: data.profiles.discriminator,
      status: data.profiles.status,
      isBot: false
    },
    content: data.content,
    timestamp: data.created_at,
    editedTimestamp: data.edited ? data.updated_at : undefined,
    attachments: [],
    reactions: [],
    mentions: [],
    referencedMessage: data.reply_to_id ? undefined : undefined
  };
};

/**
 * Delete a message
 */
export const deleteMessage = async (messageId: string): Promise<boolean> => {
  const { error } = await supabase
    .from('messages')
    .delete()
    .eq('id', messageId);

  if (error) {
    console.error('Error deleting message:', error);
    return false;
  }

  return true;
};

/**
 * Subscribe to new messages in a channel
 */
export const subscribeToChannelMessages = (
  channelId: string,
  callback: (message: Message) => void
) => {
  const subscription = supabase
    .channel(`channel-${channelId}`)
    .on(
      'postgres_changes',
      {
        event: 'INSERT',
        schema: 'public',
        table: 'messages',
        filter: `channel_id=eq.${channelId}`
      },
      async (payload) => {
        // Fetch the complete message with author info
        const { data, error } = await supabase
          .from('messages')
          .select(`
            id,
            channel_id,
            user_id,
            content,
            created_at,
            updated_at,
            edited,
            reply_to_id,
            profiles:user_id (
              id,
              username,
              discriminator,
              avatar,
              status
            )
          `)
          .eq('id', payload.new.id)
          .single();

        if (error || !data) {
          console.error('Error fetching new message:', error);
          return;
        }

        const message: Message = {
          id: data.id,
          channelId: data.channel_id,
          author: {
            id: data.profiles.id,
            username: data.profiles.username,
            avatar: data.profiles.avatar,
            discriminator: data.profiles.discriminator,
            status: data.profiles.status,
            isBot: false
          },
          content: data.content,
          timestamp: data.created_at,
          editedTimestamp: data.edited ? data.updated_at : undefined,
          attachments: [],
          reactions: [],
          mentions: [],
          referencedMessage: data.reply_to_id ? undefined : undefined
        };

        callback(message);
      }
    )
    .subscribe();

  return () => {
    supabase.removeChannel(subscription);
  };
};
