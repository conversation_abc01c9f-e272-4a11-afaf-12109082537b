// User types
export interface User {
  id: string;
  username: string;
  discriminator: string;
  avatar: string;
  status: 'online' | 'idle' | 'dnd' | 'offline';
  customStatus?: string;
  isBot?: boolean;
}

// Server types
export interface Server {
  id: string;
  name: string;
  icon: string;
  ownerId: string;
  members: ServerMember[];
  channels: Channel[];
  categories: Category[];
  roles: Role[];
}

export interface ServerMember {
  userId: string;
  nickname?: string;
  roles: string[];
  joinedAt: string;
}

export interface Role {
  id: string;
  name: string;
  color: string;
  position: number;
  permissions: string[];
  mentionable: boolean;
}

// Channel types
export interface Category {
  id: string;
  name: string;
  position: number;
  channels: string[]; // Channel IDs
}

export interface Channel {
  id: string;
  type: 'text' | 'voice' | 'announcement' | 'forum' | 'stage';
  name: string;
  topic?: string;
  position: number;
  parentId?: string; // Category ID
  lastMessageId?: string;
}

// Message types
export interface Message {
  id: string;
  channelId: string;
  author: {
    id: string;
    username: string;
    avatar: string;
    isBot: boolean;
    roles: string[];
  };
  content: string;
  timestamp: string;
  editedTimestamp?: string;
  attachments: Attachment[];
  embeds: Embed[];
  reactions: Reaction[];
  mentions: string[]; // User IDs
  referencedMessage?: Message;
}

export interface Attachment {
  id: string;
  filename: string;
  size: number;
  url: string;
  contentType?: string;
  width?: number;
  height?: number;
}

export interface Embed {
  title?: string;
  description?: string;
  url?: string;
  timestamp?: string;
  color?: number;
  image?: {
    url: string;
    width?: number;
    height?: number;
  };
  thumbnail?: {
    url: string;
    width?: number;
    height?: number;
  };
  author?: {
    name: string;
    url?: string;
    iconUrl?: string;
  };
  fields?: {
    name: string;
    value: string;
    inline?: boolean;
  }[];
}

export interface Reaction {
  emoji: {
    id?: string;
    name: string;
  };
  count: number;
  me: boolean;
}

// Direct Message types
export interface DirectMessage {
  id: string;
  sender: User;
  recipient: User;
  content: string;
  timestamp: string;
  editedTimestamp?: string;
  read: boolean;
}

// Server types
export interface Server {
  id: string;
  name: string;
  icon?: string;
  owner_id: string;
  created_at: string;
  updated_at: string;
}

export interface ServerMember {
  id: string;
  server_id: string;
  user_id: string;
  nickname?: string;
  joined_at: string;
  profiles: User;
}

// Channel types
export interface Channel {
  id: string;
  server_id: string;
  name: string;
  type: 'text' | 'voice' | 'announcement' | 'forum' | 'stage' | 'category';
  topic?: string;
  position: number;
  parent_id?: string;
  created_at: string;
  updated_at: string;
}
