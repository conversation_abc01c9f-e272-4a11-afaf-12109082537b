import React, { useState } from 'react';
import { useAuth } from '../context/AuthContext';
import UserSettings from './UserSettings';
import Tooltip from './ui/Tooltip';
import StatusIndicator from './ui/StatusIndicator';

const UserPanel: React.FC = () => {
  const { currentUser, logout } = useAuth();
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [isDeafened, setIsDeafened] = useState(false);

  if (!currentUser) return null;

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Error logging out:', error);
    }
  };

  const toggleMute = () => {
    setIsMuted(!isMuted);
    // TODO: Implement actual mute functionality
  };

  const toggleDeafen = () => {
    setIsDeafened(!isDeafened);
    if (!isDeafened) {
      setIsMuted(true); // Deafening also mutes
    }
    // TODO: Implement actual deafen functionality
  };

  return (
    <>
      <div className="h-13 bg-discord-darker border-t border-discord-separator p-2 flex items-center">
        {/* User info */}
        <div className="flex items-center flex-1 min-w-0 cursor-pointer hover:bg-discord-hover-bg rounded p-1 transition-colors">
          <div className="relative mr-2">
            <div className="w-8 h-8 rounded-full bg-discord-primary flex items-center justify-center text-white text-sm font-medium">
              {currentUser.avatar}
            </div>
            <div className="absolute -bottom-0.5 -right-0.5">
              <StatusIndicator
                status={currentUser.status}
                size="sm"
                className="border-2 border-discord-darker"
              />
            </div>
          </div>
          <div className="flex-1 min-w-0">
            <div className="text-white text-sm font-medium truncate">
              {currentUser.username}
            </div>
            <div className="text-discord-interactive-normal text-xs truncate">
              {currentUser.customStatus || `#${currentUser.discriminator}`}
            </div>
          </div>
        </div>

        {/* Control buttons */}
        <div className="flex items-center space-x-1">
          {/* Mute button */}
          <Tooltip content={isMuted ? "Unmute" : "Mute"}>
            <button
              onClick={toggleMute}
              className={`w-8 h-8 rounded flex items-center justify-center transition-colors ${
                isMuted
                  ? 'bg-discord-red text-white hover:bg-opacity-80'
                  : 'text-discord-interactive-normal hover:text-discord-interactive-hover hover:bg-discord-hover-bg'
              }`}
            >
              {isMuted ? (
                <svg width="20" height="20" viewBox="0 0 24 24">
                  <path
                    fill="currentColor"
                    d="M6.7 11H5C5 12.19 5.34 13.3 5.9 14.28L7.13 13.05C6.86 12.43 6.7 11.74 6.7 11Z"
                  />
                  <path
                    fill="currentColor"
                    d="M9.01 11.085C9.015 11.1 9.02 11.115 9.025 11.13L15.5 4.655C15.5 4.655 15.5 4.655 15.5 4.655L13.5 2.655C13.1 2.255 12.5 2.255 12.1 2.655L9.01 5.745V11.085Z"
                  />
                  <path
                    fill="currentColor"
                    d="M11.7 16.35L15.49 12.56C15.81 13.15 16 13.8 16 14.5V17.5C16 18.33 15.33 19 14.5 19H12.5C11.67 19 11 18.33 11 17.5V16.35H11.7Z"
                  />
                  <path
                    fill="currentColor"
                    d="M21 4.27L19.73 3L3 19.73L4.27 21L8.46 16.81C9.69 17.81 11.26 18.5 13 18.5C18.5 18.5 22.5 14.5 22.5 9H21C21 13.97 16.97 18 12 18C10.82 18 9.69 17.85 8.64 17.56L21 4.27Z"
                  />
                </svg>
              ) : (
                <svg width="20" height="20" viewBox="0 0 24 24">
                  <path
                    fill="currentColor"
                    d="M12 2C13.1 2 14 2.9 14 4V12C14 13.1 13.1 14 12 14C10.9 14 10 13.1 10 12V4C10 2.9 10.9 2 12 2ZM19 10V12C19 15.9 15.9 19 12 19C8.1 19 5 15.9 5 12V10H7V12C7 14.8 9.2 17 12 17C14.8 17 17 14.8 17 12V10H19ZM10 21H14V23H10V21Z"
                  />
                </svg>
              )}
            </button>
          </Tooltip>

          {/* Deafen button */}
          <Tooltip content={isDeafened ? "Undeafen" : "Deafen"}>
            <button
              onClick={toggleDeafen}
              className={`w-8 h-8 rounded flex items-center justify-center transition-colors ${
                isDeafened
                  ? 'bg-discord-red text-white hover:bg-opacity-80'
                  : 'text-discord-interactive-normal hover:text-discord-interactive-hover hover:bg-discord-hover-bg'
              }`}
            >
              {isDeafened ? (
                <svg width="20" height="20" viewBox="0 0 24 24">
                  <path
                    fill="currentColor"
                    d="M6.16204 15.0065C6.10859 15.0022 6.05455 15 6 15H4V12C4 7.588 7.589 4 12 4C13.4809 4 14.8691 4.40439 16.0599 5.14859L17.5102 3.69836C15.9292 2.61954 14.0346 2 12 2C6.486 2 2 6.485 2 12V17C2 18.104 2.896 19 4 19H6C7.104 19 8 18.104 8 17V15.0065C7.68684 14.9954 7.32951 15.0065 6.16204 15.0065Z"
                  />
                  <path
                    fill="currentColor"
                    d="M19.725 9.91686C19.9043 10.5813 20 11.2796 20 12V15H18C16.896 15 16 15.896 16 17V19C16 20.104 16.896 21 18 21H20C21.105 21 22 20.104 22 19V12C22 10.7075 21.7536 9.47149 21.3053 8.33658L19.725 9.91686Z"
                  />
                  <path
                    fill="currentColor"
                    d="M3.20101 23.6243L1.7868 22.2101L21.5858 2.41113L23 3.82535L3.20101 23.6243Z"
                  />
                </svg>
              ) : (
                <svg width="20" height="20" viewBox="0 0 24 24">
                  <path
                    fill="currentColor"
                    d="M12 2.00305C6.486 2.00305 2 6.48805 2 12.0031V20.0031C2 21.1071 2.895 22.0031 4 22.0031H6C7.104 22.0031 8 21.1071 8 20.0031V17.0031C8 15.8991 7.104 15.0031 6 15.0031H4V12.0031C4 7.59105 7.589 4.00305 12 4.00305C16.411 4.00305 20 7.59105 20 12.0031V15.0031H18C16.896 15.0031 16 15.8991 16 17.0031V20.0031C16 21.1071 16.896 22.0031 18 22.0031H20C21.104 22.0031 22 21.1071 22 20.0031V12.0031C22 6.48805 17.514 2.00305 12 2.00305Z"
                  />
                </svg>
              )}
            </button>
          </Tooltip>

          {/* Settings button */}
          <Tooltip content="User Settings">
            <button
              onClick={() => setIsSettingsOpen(true)}
              className="w-8 h-8 rounded flex items-center justify-center text-discord-interactive-normal hover:text-discord-interactive-hover hover:bg-discord-hover-bg transition-colors"
            >
              <svg width="20" height="20" viewBox="0 0 24 24">
                <path
                  fill="currentColor"
                  d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.8,11.69,4.8,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"
                />
              </svg>
            </button>
          </Tooltip>
        </div>
      </div>

      <UserSettings isOpen={isSettingsOpen} onClose={() => setIsSettingsOpen(false)} />
    </>
  );
};

export default UserPanel;
