import React, { useState } from 'react';
import { createChannel } from '../../services/channelService';
import { Channel } from '../../types';
import ChannelIcon from '../ui/ChannelIcon';

interface CreateChannelModalProps {
  isOpen: boolean;
  onClose: () => void;
  onChannelCreated: (channel: Channel) => void;
  serverId: string;
}

const CreateChannelModal: React.FC<CreateChannelModalProps> = ({
  isOpen,
  onClose,
  onChannelCreated,
  serverId
}) => {
  const [channelType, setChannelType] = useState<'text' | 'voice' | 'announcement'>('text');
  const [channelName, setChannelName] = useState('');
  const [channelTopic, setChannelTopic] = useState('');
  const [isPrivate, setIsPrivate] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const handleCreateChannel = async () => {
    if (!channelName.trim()) {
      setError('Channel name is required');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      const newChannel = await createChannel(
        serverId,
        channelName.toLowerCase().replace(/\s+/g, '-'),
        channelType,
        { topic: channelTopic }
      );

      if (newChannel) {
        onChannelCreated(newChannel);
        onClose();
        resetForm();
      } else {
        setError('Failed to create channel');
      }
    } catch (err) {
      setError('An error occurred while creating the channel');
    } finally {
      setIsLoading(false);
    }
  };

  const resetForm = () => {
    setChannelType('text');
    setChannelName('');
    setChannelTopic('');
    setIsPrivate(false);
    setError('');
  };

  const handleClose = () => {
    onClose();
    resetForm();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center z-50">
      <div className="bg-discord-modal-bg rounded-lg w-full max-w-md mx-4 overflow-hidden">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-white">
              Create Channel
            </h2>
            <button
              onClick={handleClose}
              className="text-discord-interactive-normal hover:text-discord-interactive-hover"
            >
              <svg width="24" height="24" viewBox="0 0 24 24">
                <path
                  fill="currentColor"
                  d="M18.4 4L12 10.4L5.6 4L4 5.6L10.4 12L4 18.4L5.6 20L12 13.6L18.4 20L20 18.4L13.6 12L20 5.6L18.4 4Z"
                />
              </svg>
            </button>
          </div>

          <div className="mb-6">
            <h3 className="text-sm font-medium text-discord-header-secondary mb-3 uppercase">
              Channel Type
            </h3>
            
            <div className="space-y-2">
              <label className="flex items-center p-3 rounded-md border border-discord-separator hover:border-discord-interactive-normal cursor-pointer">
                <input
                  type="radio"
                  name="channelType"
                  value="text"
                  checked={channelType === 'text'}
                  onChange={(e) => setChannelType(e.target.value as 'text')}
                  className="sr-only"
                />
                <div className={`w-6 h-6 rounded-full border-2 mr-3 flex items-center justify-center ${
                  channelType === 'text' 
                    ? 'border-discord-primary bg-discord-primary' 
                    : 'border-discord-interactive-normal'
                }`}>
                  {channelType === 'text' && (
                    <div className="w-2 h-2 bg-white rounded-full" />
                  )}
                </div>
                <div className="flex items-center flex-1">
                  <ChannelIcon type="text" className="text-discord-interactive-normal mr-3" />
                  <div>
                    <div className="text-white font-medium">Text</div>
                    <div className="text-sm text-discord-text-muted">
                      Send messages, images, GIFs, emoji, opinions, and puns
                    </div>
                  </div>
                </div>
              </label>

              <label className="flex items-center p-3 rounded-md border border-discord-separator hover:border-discord-interactive-normal cursor-pointer">
                <input
                  type="radio"
                  name="channelType"
                  value="voice"
                  checked={channelType === 'voice'}
                  onChange={(e) => setChannelType(e.target.value as 'voice')}
                  className="sr-only"
                />
                <div className={`w-6 h-6 rounded-full border-2 mr-3 flex items-center justify-center ${
                  channelType === 'voice' 
                    ? 'border-discord-primary bg-discord-primary' 
                    : 'border-discord-interactive-normal'
                }`}>
                  {channelType === 'voice' && (
                    <div className="w-2 h-2 bg-white rounded-full" />
                  )}
                </div>
                <div className="flex items-center flex-1">
                  <ChannelIcon type="voice" className="text-discord-interactive-normal mr-3" />
                  <div>
                    <div className="text-white font-medium">Voice</div>
                    <div className="text-sm text-discord-text-muted">
                      Hang out together with voice, video, and screen share
                    </div>
                  </div>
                </div>
              </label>

              <label className="flex items-center p-3 rounded-md border border-discord-separator hover:border-discord-interactive-normal cursor-pointer">
                <input
                  type="radio"
                  name="channelType"
                  value="announcement"
                  checked={channelType === 'announcement'}
                  onChange={(e) => setChannelType(e.target.value as 'announcement')}
                  className="sr-only"
                />
                <div className={`w-6 h-6 rounded-full border-2 mr-3 flex items-center justify-center ${
                  channelType === 'announcement' 
                    ? 'border-discord-primary bg-discord-primary' 
                    : 'border-discord-interactive-normal'
                }`}>
                  {channelType === 'announcement' && (
                    <div className="w-2 h-2 bg-white rounded-full" />
                  )}
                </div>
                <div className="flex items-center flex-1">
                  <ChannelIcon type="announcement" className="text-discord-interactive-normal mr-3" />
                  <div>
                    <div className="text-white font-medium">Announcement</div>
                    <div className="text-sm text-discord-text-muted">
                      Important updates for your community
                    </div>
                  </div>
                </div>
              </label>
            </div>
          </div>

          {error && (
            <div className="mb-4 p-3 bg-discord-red text-white rounded-md text-sm">
              {error}
            </div>
          )}

          <div className="mb-4">
            <label className="block text-sm font-medium text-discord-header-secondary mb-2 uppercase">
              Channel Name
            </label>
            <div className="relative">
              <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
                <ChannelIcon type={channelType} className="text-discord-interactive-muted" />
              </div>
              <input
                type="text"
                value={channelName}
                onChange={(e) => setChannelName(e.target.value)}
                placeholder="new-channel"
                className="w-full pl-10 pr-3 py-2 bg-discord-input-bg text-white border border-discord-separator rounded-md focus:outline-none focus:border-discord-primary"
                maxLength={100}
              />
            </div>
          </div>

          {channelType === 'text' && (
            <div className="mb-4">
              <label className="block text-sm font-medium text-discord-header-secondary mb-2 uppercase">
                Channel Topic (Optional)
              </label>
              <input
                type="text"
                value={channelTopic}
                onChange={(e) => setChannelTopic(e.target.value)}
                placeholder="Let everyone know what this channel is for"
                className="w-full px-3 py-2 bg-discord-input-bg text-white border border-discord-separator rounded-md focus:outline-none focus:border-discord-primary"
                maxLength={1024}
              />
            </div>
          )}

          <div className="mb-6">
            <label className="flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={isPrivate}
                onChange={(e) => setIsPrivate(e.target.checked)}
                className="sr-only"
              />
              <div className={`w-6 h-6 rounded border-2 mr-3 flex items-center justify-center ${
                isPrivate 
                  ? 'border-discord-primary bg-discord-primary' 
                  : 'border-discord-interactive-normal'
              }`}>
                {isPrivate && (
                  <svg width="16" height="16" viewBox="0 0 24 24" className="text-white">
                    <path
                      fill="currentColor"
                      d="M9 16.17L4.83 12L3.41 13.41L9 19L21 7L19.59 5.59L9 16.17Z"
                    />
                  </svg>
                )}
              </div>
              <div>
                <div className="text-white font-medium">🔒 Private Channel</div>
                <div className="text-sm text-discord-text-muted">
                  Only selected members and roles will be able to view this channel
                </div>
              </div>
            </label>
          </div>

          <div className="flex justify-end space-x-3">
            <button
              onClick={handleClose}
              className="px-4 py-2 text-discord-interactive-normal hover:text-discord-interactive-hover"
              disabled={isLoading}
            >
              Cancel
            </button>
            <button
              onClick={handleCreateChannel}
              disabled={!channelName.trim() || isLoading}
              className="px-6 py-2 bg-discord-primary text-white rounded-md hover:bg-opacity-90 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? 'Creating...' : 'Create Channel'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CreateChannelModal;
