Set objWShell = CreateObject("WScript.Shell")
strDesktop = objWShell.SpecialFolders("Desktop")

' Create desktop shortcut
Set objShortcut = objWShell.CreateShortcut(strDesktop & "\Discord Clone.lnk")
objShortcut.TargetPath = objWShell.CurrentDirectory & "\DiscordClone.hta"
objShortcut.WorkingDirectory = objWShell.CurrentDirectory
objShortcut.Description = "Discord Clone Application"
objShortcut.IconLocation = objWShell.CurrentDirectory & "\discord_icon.ico"
objShortcut.Save

' Create Start Menu shortcut
strStartMenu = objWShell.SpecialFolders("StartMenu") & "\Programs"
Set objShortcut = objWShell.CreateShortcut(strStartMenu & "\Discord Clone.lnk")
objShortcut.TargetPath = objWShell.CurrentDirectory & "\DiscordClone.hta"
objShortcut.WorkingDirectory = objWShell.CurrentDirectory
objShortcut.Description = "Discord Clone Application"
objShortcut.IconLocation = objWShell.CurrentDirectory & "\discord_icon.ico"
objShortcut.Save

MsgBox "Shortcuts created successfully on Desktop and Start Menu!", vbInformation, "Discord Clone"
