import React from 'react';
import { Link, useLocation } from 'react-router-dom';

// Mock data for servers
const servers = [
  { id: '1', name: 'General', icon: '🌐' },
  { id: '2', name: 'Gaming', icon: '🎮' },
  { id: '3', name: 'Music', icon: '🎵' },
  { id: '4', name: 'Art', icon: '🎨' },
];

const Sidebar: React.FC = () => {
  const location = useLocation();
  const isActive = (path: string) => location.pathname.includes(path);

  return (
    <div className="w-[72px] bg-discord-dark-bg flex flex-col items-center py-3 space-y-2">
      {/* Home button */}
      <Link 
        to="/" 
        className={`w-12 h-12 rounded-full flex items-center justify-center transition-all hover:rounded-2xl ${
          location.pathname === '/' ? 'bg-discord-primary text-white' : 'bg-discord-light-bg text-discord-primary hover:bg-discord-primary hover:text-white'
        }`}
      >
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-6 h-6">
          <path d="M11.47 3.84a.75.75 0 011.06 0l8.69 8.69a.75.75 0 101.06-1.06l-8.689-8.69a2.25 2.25 0 00-3.182 0l-8.69 8.69a.75.75 0 001.061 1.06l8.69-8.69z" />
          <path d="M12 5.432l8.159 8.159c.03.03.06.058.091.086v6.198c0 1.035-.84 1.875-1.875 1.875H15a.75.75 0 01-.75-.75v-4.5a.75.75 0 00-.75-.75h-3a.75.75 0 00-.75.75V21a.75.75 0 01-.75.75H5.625a1.875 1.875 0 01-1.875-1.875v-6.198a2.29 2.29 0 00.091-.086L12 5.43z" />
        </svg>
      </Link>

      {/* Separator */}
      <div className="w-8 h-0.5 bg-discord-separator rounded-full"></div>

      {/* Server list */}
      <div className="flex flex-col items-center space-y-2 overflow-y-auto">
        {servers.map((server) => (
          <Link
            key={server.id}
            to={`/channels/${server.id}/1`}
            className={`w-12 h-12 rounded-full flex items-center justify-center transition-all hover:rounded-2xl ${
              isActive(`/channels/${server.id}`) ? 'bg-discord-primary text-white' : 'bg-discord-light-bg text-white hover:bg-discord-primary'
            }`}
            title={server.name}
          >
            {server.icon}
          </Link>
        ))}
      </div>

      {/* Add server button */}
      <button 
        className="w-12 h-12 rounded-full bg-discord-light-bg text-discord-green hover:bg-discord-green hover:text-white transition-all hover:rounded-2xl flex items-center justify-center"
        title="Add a Server"
      >
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-6 h-6">
          <path fillRule="evenodd" d="M12 3.75a.75.75 0 01.75.75v6.75h6.75a.75.75 0 010 1.5h-6.75v6.75a.75.75 0 01-1.5 0v-6.75H4.5a.75.75 0 010-1.5h6.75V4.5a.75.75 0 01.75-.75z" clipRule="evenodd" />
        </svg>
      </button>

      {/* Explore public servers */}
      <button 
        className="w-12 h-12 rounded-full bg-discord-light-bg text-discord-green hover:bg-discord-green hover:text-white transition-all hover:rounded-2xl flex items-center justify-center"
        title="Explore Public Servers"
      >
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-6 h-6">
          <path fillRule="evenodd" d="M10.5 3.75a6.75 6.75 0 100 13.5 6.75 6.75 0 000-13.5zM2.25 10.5a8.25 8.25 0 1114.59 5.28l4.69 4.69a.75.75 0 11-1.06 1.06l-4.69-4.69A8.25 8.25 0 012.25 10.5z" clipRule="evenodd" />
        </svg>
      </button>
    </div>
  );
};

export default Sidebar;
