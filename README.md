# Discord Clone

A modern Discord clone built with React, TypeScript, and TailwindCSS.

## Features

- **Authentication**: Login and registration with email/password
- **Server Management**: Create and join servers
- **Channel System**: Text and voice channels
- **Messaging**: Real-time messaging with rich text formatting
- **User Profiles**: Customizable user profiles with status indicators
- **Responsive Design**: Works on desktop, tablet, and mobile devices

## Technologies Used

- **React**: Frontend library for building user interfaces
- **TypeScript**: Static typing for JavaScript
- **TailwindCSS**: Utility-first CSS framework
- **React Router**: Client-side routing
- **Zustand**: State management
- **Supabase**: Backend services (authentication, database, storage)

## Getting Started

### Prerequisites

- Node.js (v14 or higher)
- npm or yarn

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/discord-clone.git
   cd discord-clone
   ```

2. Install dependencies:
   ```bash
   npm install
   # or
   yarn install
   ```

3. Start the development server:
   ```bash
   npm start
   # or
   yarn start
   ```

4. Set up Supabase:
   - Create a Supabase account at [https://supabase.com](https://supabase.com)
   - Create a new project
   - Copy your Supabase URL and anon key from the project settings
   - Create a `.env.local` file in the root directory (copy from `.env.example`)
   - Add your Supabase URL and anon key to the `.env.local` file

5. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Project Structure

```
discord-clone/
├── public/              # Static files
├── src/                 # Source code
│   ├── assets/          # Images, fonts, etc.
│   ├── components/      # Reusable components
│   ├── context/         # React context providers
│   ├── hooks/           # Custom React hooks
│   ├── pages/           # Page components
│   ├── services/        # API services
│   ├── styles/          # Global styles
│   ├── types/           # TypeScript type definitions
│   ├── utils/           # Utility functions
│   ├── App.tsx          # Main App component
│   └── index.tsx        # Entry point
├── .gitignore           # Git ignore file
├── package.json         # Project dependencies
├── README.md            # Project documentation
├── tailwind.config.js   # TailwindCSS configuration
└── tsconfig.json        # TypeScript configuration
```

## Deployment

This project can be deployed to various platforms:

- **Vercel**: Recommended for easy deployment
- **Netlify**: Another great option for frontend deployment
- **Supabase Hosting**: Good option if using Supabase services

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Discord for the inspiration
- The React and TypeScript communities for their excellent documentation
- TailwindCSS for making styling a breeze
