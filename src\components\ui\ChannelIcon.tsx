import React from 'react';

interface ChannelIconProps {
  type: 'text' | 'voice' | 'announcement' | 'forum' | 'stage' | 'category';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

const ChannelIcon: React.FC<ChannelIconProps> = ({
  type,
  size = 'md',
  className = ''
}) => {
  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'w-4 h-4';
      case 'md':
        return 'w-5 h-5';
      case 'lg':
        return 'w-6 h-6';
      default:
        return 'w-5 h-5';
    }
  };

  const getIcon = () => {
    switch (type) {
      case 'text':
        return (
          <svg viewBox="0 0 24 24" className={`${getSizeClasses()} ${className}`}>
            <path
              fill="currentColor"
              d="M5.88657 21C5.57547 21 5.3399 20.7189 5.39427 20.4126L6.00001 17H2.59511C2.28449 17 2.04905 16.7198 2.10259 16.4138L2.27759 15.4138C2.31946 15.1746 2.52722 15 2.77011 15H6.35001L7.41001 9H4.00511C3.69449 9 3.45905 8.71977 3.51259 8.41381L3.68759 7.41381C3.72946 7.17456 3.93722 7 4.18011 7H7.76001L8.39677 3.41262C8.43914 3.17391 8.64664 3 8.88907 3H9.87344C10.1845 3 10.4201 3.28107 10.3657 3.58738L9.76001 7H15.76L16.3968 3.41262C16.4391 3.17391 16.6466 3 16.8891 3H17.8734C18.1845 3 18.4201 3.28107 18.3657 3.58738L17.76 7H21.1649C21.4755 7 21.711 7.28023 21.6574 7.58619L21.4824 8.58619C21.4406 8.82544 21.2328 9 20.9899 9H17.41L16.35 15H19.7549C20.0655 15 20.301 15.2802 20.2474 15.5862L20.0724 16.5862C20.0306 16.8254 19.8228 17 19.5799 17H16L15.3632 20.5874C15.3209 20.8261 15.1134 21 14.8709 21H13.8866C13.5755 21 13.3399 20.7189 13.3943 20.4126L14 17H8.00001L7.36325 20.5874C7.32088 20.8261 7.11337 21 6.87094 21H5.88657ZM9.41001 9L8.35001 15H14.35L15.41 9H9.41001Z"
            />
          </svg>
        );
      
      case 'voice':
        return (
          <svg viewBox="0 0 24 24" className={`${getSizeClasses()} ${className}`}>
            <path
              fill="currentColor"
              d="M12 2.00305C6.486 2.00305 2 6.48805 2 12.0031V20.0031C2 21.1071 2.895 22.0031 4 22.0031H6C7.104 22.0031 8 21.1071 8 20.0031V17.0031C8 15.8991 7.104 15.0031 6 15.0031H4V12.0031C4 7.59105 7.589 4.00305 12 4.00305C16.411 4.00305 20 7.59105 20 12.0031V15.0031H18C16.896 15.0031 16 15.8991 16 17.0031V20.0031C16 21.1071 16.896 22.0031 18 22.0031H20C21.104 22.0031 22 21.1071 22 20.0031V12.0031C22 6.48805 17.514 2.00305 12 2.00305Z"
            />
          </svg>
        );
      
      case 'announcement':
        return (
          <svg viewBox="0 0 24 24" className={`${getSizeClasses()} ${className}`}>
            <path
              fill="currentColor"
              d="M12.0002 2C13.1048 2 14.0002 2.89543 14.0002 4V6.26756C16.9492 6.82973 19.2656 9.14613 19.8278 12.0951C19.9425 12.7102 19.4479 13.2048 18.8328 13.0901C18.2177 12.9754 17.7231 12.4808 17.8378 11.8657C17.4227 9.70301 15.6293 8.00043 13.4002 8.00043H10.6002C8.37106 8.00043 6.57764 9.70301 6.16256 11.8657C6.04787 12.4808 5.55327 12.9754 4.93817 13.0901C4.32307 13.2048 3.82847 12.7102 3.94316 12.0951C4.50533 9.14613 6.82173 6.82973 9.77073 6.26756V4C9.77073 2.89543 10.6662 2 11.7707 2H12.0002ZM12.0002 15C12.5525 15 13.0002 15.4477 13.0002 16V18C13.0002 18.5523 12.5525 19 12.0002 19C11.4479 19 11.0002 18.5523 11.0002 18V16C11.0002 15.4477 11.4479 15 12.0002 15ZM8.00024 20C8.00024 18.8954 8.89567 18 10.0002 18H14.0002C15.1048 18 16.0002 18.8954 16.0002 20C16.0002 21.1046 15.1048 22 14.0002 22H10.0002C8.89567 22 8.00024 21.1046 8.00024 20Z"
            />
          </svg>
        );
      
      case 'forum':
        return (
          <svg viewBox="0 0 24 24" className={`${getSizeClasses()} ${className}`}>
            <path
              fill="currentColor"
              d="M4.79805 3C3.80445 3 2.99805 3.8055 2.99805 4.8V15.6C2.99805 16.5936 3.80445 17.4 4.79805 17.4H7.49805V21L11.098 17.4H19.198C20.1916 17.4 20.998 16.5936 20.998 15.6V4.8C20.998 3.8055 20.1916 3 19.198 3H4.79805ZM7.49805 7.2H16.498C16.9412 7.2 17.298 7.5568 17.298 8C17.298 8.4432 16.9412 8.8 16.498 8.8H7.49805C7.05485 8.8 6.69805 8.4432 6.69805 8C6.69805 7.5568 7.05485 7.2 7.49805 7.2ZM7.49805 10.4H13.298C13.7412 10.4 14.098 10.7568 14.098 11.2C14.098 11.6432 13.7412 12 13.298 12H7.49805C7.05485 12 6.69805 11.6432 6.69805 11.2C6.69805 10.7568 7.05485 10.4 7.49805 10.4Z"
            />
          </svg>
        );
      
      case 'stage':
        return (
          <svg viewBox="0 0 24 24" className={`${getSizeClasses()} ${className}`}>
            <path
              fill="currentColor"
              d="M14 13C14 14.1 13.1 15 12 15C10.9 15 10 14.1 10 13C10 11.9 10.9 11 12 11C13.1 11 14 11.9 14 13ZM12 19C15.31 19 18 16.31 18 13C18 9.69 15.31 7 12 7C8.69 7 6 9.69 6 13C6 16.31 8.69 19 12 19ZM12 5C16.42 5 20 8.58 20 13C20 17.42 16.42 21 12 21C7.58 21 4 17.42 4 13C4 8.58 7.58 5 12 5ZM12 3C6.48 3 2 7.48 2 13C2 18.52 6.48 23 12 23C17.52 23 22 18.52 22 13C22 7.48 17.52 3 12 3Z"
            />
          </svg>
        );
      
      case 'category':
        return (
          <svg viewBox="0 0 24 24" className={`${getSizeClasses()} ${className}`}>
            <path
              fill="currentColor"
              d="M16.59 8.59L12 13.17L7.41 8.59L6 10L12 16L18 10L16.59 8.59Z"
            />
          </svg>
        );
      
      default:
        return (
          <svg viewBox="0 0 24 24" className={`${getSizeClasses()} ${className}`}>
            <path
              fill="currentColor"
              d="M5.88657 21C5.57547 21 5.3399 20.7189 5.39427 20.4126L6.00001 17H2.59511C2.28449 17 2.04905 16.7198 2.10259 16.4138L2.27759 15.4138C2.31946 15.1746 2.52722 15 2.77011 15H6.35001L7.41001 9H4.00511C3.69449 9 3.45905 8.71977 3.51259 8.41381L3.68759 7.41381C3.72946 7.17456 3.93722 7 4.18011 7H7.76001L8.39677 3.41262C8.43914 3.17391 8.64664 3 8.88907 3H9.87344C10.1845 3 10.4201 3.28107 10.3657 3.58738L9.76001 7H15.76L16.3968 3.41262C16.4391 3.17391 16.6466 3 16.8891 3H17.8734C18.1845 3 18.4201 3.28107 18.3657 3.58738L17.76 7H21.1649C21.4755 7 21.711 7.28023 21.6574 7.58619L21.4824 8.58619C21.4406 8.82544 21.2328 9 20.9899 9H17.41L16.35 15H19.7549C20.0655 15 20.301 15.2802 20.2474 15.5862L20.0724 16.5862C20.0306 16.8254 19.8228 17 19.5799 17H16L15.3632 20.5874C15.3209 20.8261 15.1134 21 14.8709 21H13.8866C13.5755 21 13.3399 20.7189 13.3943 20.4126L14 17H8.00001L7.36325 20.5874C7.32088 20.8261 7.11337 21 6.87094 21H5.88657ZM9.41001 9L8.35001 15H14.35L15.41 9H9.41001Z"
            />
          </svg>
        );
    }
  };

  return getIcon();
};

export default ChannelIcon;
